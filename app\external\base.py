"""
外部数据源基类
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import date
from app.core.logging import get_logger

logger = get_logger(__name__)


class BaseDataSource(ABC):
    """外部数据源基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = get_logger(f"datasource.{name}")
    
    @abstractmethod
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """
        获取股票列表
        
        Returns:
            股票列表
        """
        pass
    
    @abstractmethod
    async def get_stock_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票信息
        """
        pass
    
    @abstractmethod
    async def get_stock_price(
        self, 
        stock_code: str, 
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """
        获取股票价格数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            价格数据列表
        """
        pass
    
    @abstractmethod
    async def get_realtime_price(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        获取实时价格
        
        Args:
            stock_code: 股票代码
            
        Returns:
            实时价格数据
        """
        pass
    
    @abstractmethod
    async def get_market_indices(self) -> List[Dict[str, Any]]:
        """
        获取市场指数
        
        Returns:
            指数数据列表
        """
        pass
    
    async def test_connection(self) -> bool:
        """
        测试连接
        
        Returns:
            是否连接成功
        """
        try:
            # 尝试获取股票列表来测试连接
            stocks = await self.get_stock_list()
            return len(stocks) > 0
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
    
    def log_error(self, operation: str, error: Exception):
        """记录错误日志"""
        self.logger.error(f"{operation}失败: {error}")
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
