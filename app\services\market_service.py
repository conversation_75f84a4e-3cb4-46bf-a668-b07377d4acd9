"""
市场数据业务逻辑服务
"""

from typing import List, Optional
from datetime import date
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.market_repository import (
    MarketIndexRepository,
    IndustryRepository,
    IndustryDailyRepository,
    TradingCalendarRepository,
)
from app.external.akshare_client import AKShareClient
from app.external.tushare_client import TushareClient
from app.schemas.market import MarketIndex, Industry, IndustryDaily, MarketOverview
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class MarketService(BaseService):
    """市场数据服务"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.index_repo = MarketIndexRepository(db)
        self.industry_repo = IndustryRepository(db)
        self.industry_daily_repo = IndustryDailyRepository(db)
        self.calendar_repo = TradingCalendarRepository(db)
        
        # 初始化数据源
        self.akshare_client = AKShareClient()
        self.tushare_client = TushareClient()
    
    async def get_market_indices(self, codes: List[str]) -> List[MarketIndex]:
        """
        获取市场指数
        
        Args:
            codes: 指数代码列表
            
        Returns:
            指数列表
        """
        cache_key = self.generate_cache_key("market:indices", ":".join(sorted(codes)))
        
        async def fetch_data():
            indices = await self.index_repo.get_latest_indices(codes)
            return [MarketIndex.model_validate(index) for index in indices]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_market_data_ttl,
        )
    
    async def get_index_history(
        self,
        code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100,
    ) -> List[MarketIndex]:
        """
        获取指数历史数据
        
        Args:
            code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制数量
            
        Returns:
            指数历史数据
        """
        cache_key = self.generate_cache_key(
            "market:index:history",
            code,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
        )
        
        async def fetch_data():
            history = await self.index_repo.get_index_history(
                code=code,
                start_date=start_date,
                end_date=end_date,
                limit=limit,
            )
            return [MarketIndex.model_validate(item) for item in history]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_market_data_ttl,
        )
    
    async def get_industries(self, level: int = 1) -> List[Industry]:
        """
        获取行业列表
        
        Args:
            level: 行业级别
            
        Returns:
            行业列表
        """
        cache_key = self.generate_cache_key("market:industries", level=level)
        
        async def fetch_data():
            industries = await self.industry_repo.get_by_level(level)
            return [Industry.model_validate(industry) for industry in industries]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_market_data_ttl,
        )
    
    async def get_industry_children(self, parent_code: str) -> List[Industry]:
        """
        获取子行业
        
        Args:
            parent_code: 父级行业代码
            
        Returns:
            子行业列表
        """
        cache_key = self.generate_cache_key("market:industry:children", parent_code)
        
        async def fetch_data():
            children = await self.industry_repo.get_children(parent_code)
            return [Industry.model_validate(child) for child in children]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_market_data_ttl,
        )
    
    async def get_hot_industries(self, limit: int = 20) -> List[IndustryDaily]:
        """
        获取热门行业
        
        Args:
            limit: 限制数量
            
        Returns:
            热门行业列表
        """
        cache_key = self.generate_cache_key("market:industries:hot", limit=limit)
        
        async def fetch_data():
            hot_industries = await self.industry_daily_repo.get_latest_industry_data(limit)
            return [IndustryDaily.model_validate(industry) for industry in hot_industries]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_market_data_ttl,
        )
    
    async def get_market_overview(self) -> MarketOverview:
        """
        获取市场概览
        
        Returns:
            市场概览
        """
        cache_key = "market:overview"
        
        async def fetch_data():
            # 获取主要指数
            main_indices = await self.get_market_indices(["000001", "399001", "399006"])
            
            # 获取热门行业
            hot_industries = await self.get_hot_industries(10)
            
            # 构建市场统计
            market_stats = {
                "total_indices": len(main_indices),
                "hot_industries_count": len(hot_industries),
                "update_time": "2024-01-01T00:00:00Z",  # 实际应该是最新更新时间
                "trading_status": await self._get_trading_status(),
            }
            
            return MarketOverview(
                indices=main_indices,
                hot_industries=hot_industries,
                market_stats=market_stats,
            )
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_market_data_ttl,
        )
    
    async def is_trading_day(self, cal_date: Optional[date] = None) -> bool:
        """
        检查是否为交易日
        
        Args:
            cal_date: 日期，默认为今天
            
        Returns:
            是否为交易日
        """
        if not cal_date:
            cal_date = date.today()
        
        cache_key = self.generate_cache_key("market:trading_day", cal_date)
        
        async def fetch_data():
            return await self.calendar_repo.is_trading_day(cal_date)
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=86400,  # 缓存一天
        )
    
    async def get_latest_trading_day(self) -> Optional[date]:
        """
        获取最新交易日
        
        Returns:
            最新交易日
        """
        cache_key = "market:latest_trading_day"
        
        async def fetch_data():
            return await self.calendar_repo.get_latest_trading_day()
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=3600,  # 缓存1小时
        )
    
    async def update_market_data(self) -> bool:
        """
        更新市场数据
        
        Returns:
            是否更新成功
        """
        try:
            # 优先使用Tushare，备用AKShare
            data_source = self.tushare_client if self.tushare_client.enabled else self.akshare_client
            
            if not data_source.enabled:
                logger.warning("没有可用的数据源")
                return False
            
            # 更新市场指数
            indices_data = await data_source.get_market_indices()
            for index_data in indices_data:
                # 这里需要实现指数数据的插入或更新逻辑
                pass
            
            # 清除相关缓存
            await self.invalidate_cache("market:overview")
            await self.invalidate_cache("market:indices:*")
            
            logger.info("市场数据更新成功")
            return True
            
        except Exception as e:
            logger.error(f"更新市场数据失败: {e}")
            return False
    
    async def _get_trading_status(self) -> str:
        """
        获取交易状态
        
        Returns:
            交易状态
        """
        try:
            is_trading = await self.is_trading_day()
            if is_trading:
                # 这里可以进一步判断是否在交易时间内
                return "trading"
            else:
                return "closed"
        except:
            return "unknown"
