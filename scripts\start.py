"""
应用启动脚本
"""

import asyncio
import uvicorn
from app.core.config import settings
from app.core.logging import configure_logging, get_logger

# 配置日志
configure_logging()
logger = get_logger(__name__)


async def main():
    """主函数"""
    logger.info(f"启动{settings.app_name} v{settings.app_version}")
    logger.info(f"调试模式: {settings.debug}")
    logger.info(f"数据库URL: {settings.database_url}")
    logger.info(f"Redis URL: {settings.redis_url}")
    
    # 启动服务器
    config = uvicorn.Config(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True,
    )
    
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    asyncio.run(main())
