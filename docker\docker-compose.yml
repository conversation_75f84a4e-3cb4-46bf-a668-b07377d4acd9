version: '3.8'

services:
  api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: quantitative-api
    restart: always
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    env_file:
      - ../.env
    volumes:
      - ../app:/app/app
    networks:
      - quantitative-network

  db:
    image: postgres:14-alpine
    container_name: quantitative-db
    restart: always
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-quantitative_db}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - quantitative-network

  redis:
    image: redis:7-alpine
    container_name: quantitative-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quantitative-network

  pgadmin:
    image: dpage/pgadmin4
    container_name: quantitative-pgadmin
    restart: always
    ports:
      - "5050:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD:-admin}
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - db
    networks:
      - quantitative-network

networks:
  quantitative-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  pgadmin_data:
