"""
API依赖注入
"""

from typing import AsyncGenerator
from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import verify_token
from app.repositories.stock_repository import (
    StockRepository, 
    StockPriceRepository, 
    StockRealtimeRepository
)
from app.repositories.market_repository import (
    MarketIndexRepository,
    IndustryRepository,
    IndustryDailyRepository,
    TradingCalendarRepository,
)

# HTTP Bearer认证
security = HTTPBearer(auto_error=False)


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """
    获取当前用户
    
    Args:
        credentials: 认证凭据
        
    Returns:
        用户ID
        
    Raises:
        HTTPException: 认证失败
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token_data = verify_token(credentials.credentials)
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return token_data


# 仓储依赖
async def get_stock_repository(
    db: AsyncSession = Depends(get_db)
) -> StockRepository:
    """获取股票仓储"""
    return StockRepository(db)


async def get_stock_price_repository(
    db: AsyncSession = Depends(get_db)
) -> StockPriceRepository:
    """获取股票价格仓储"""
    return StockPriceRepository(db)


async def get_stock_realtime_repository(
    db: AsyncSession = Depends(get_db)
) -> StockRealtimeRepository:
    """获取股票实时数据仓储"""
    return StockRealtimeRepository(db)


async def get_market_index_repository(
    db: AsyncSession = Depends(get_db)
) -> MarketIndexRepository:
    """获取市场指数仓储"""
    return MarketIndexRepository(db)


async def get_industry_repository(
    db: AsyncSession = Depends(get_db)
) -> IndustryRepository:
    """获取行业仓储"""
    return IndustryRepository(db)


async def get_industry_daily_repository(
    db: AsyncSession = Depends(get_db)
) -> IndustryDailyRepository:
    """获取行业日线数据仓储"""
    return IndustryDailyRepository(db)


async def get_trading_calendar_repository(
    db: AsyncSession = Depends(get_db)
) -> TradingCalendarRepository:
    """获取交易日历仓储"""
    return TradingCalendarRepository(db)
