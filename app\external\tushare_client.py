"""
Tushare数据源客户端
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import date, datetime
import pandas as pd

from app.external.base import BaseDataSource
from app.core.config import settings


class TushareClient(BaseDataSource):
    """Tushare数据源客户端"""
    
    def __init__(self):
        super().__init__("tushare")
        self.token = settings.tushare_token
        self.enabled = bool(self.token)
        
        if self.enabled:
            try:
                import tushare as ts
                ts.set_token(self.token)
                self.ts = ts
                self.pro = ts.pro_api()
                self.log_info("Tushare客户端初始化成功")
            except ImportError:
                self.log_error("初始化", Exception("Tushare未安装"))
                self.enabled = False
            except Exception as e:
                self.log_error("初始化", e)
                self.enabled = False
        else:
            self.log_info("Tushare客户端已禁用(未配置token)")
    
    async def _run_in_executor(self, func, *args, **kwargs):
        """在线程池中运行同步函数"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, func, *args, **kwargs)
    
    def _convert_stock_code(self, code: str) -> str:
        """转换股票代码格式为Tushare格式"""
        if code.startswith("6"):
            return f"{code}.SH"
        else:
            return f"{code}.SZ"
    
    def _parse_stock_code(self, ts_code: str) -> str:
        """解析Tushare股票代码为标准格式"""
        return ts_code.split(".")[0]
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        if not self.enabled:
            return []
        
        try:
            # 获取股票基本信息
            df = await self._run_in_executor(
                self.pro.stock_basic,
                exchange="",
                list_status="L",
                fields="ts_code,symbol,name,area,industry,market,list_date"
            )
            
            stocks = []
            for _, row in df.iterrows():
                stock = {
                    "code": row["symbol"],
                    "name": row["name"],
                    "market": row["market"],
                    "industry": row["industry"],
                    "list_date": pd.to_datetime(row["list_date"]).date() if row["list_date"] else None,
                }
                stocks.append(stock)
            
            self.log_info(f"获取股票列表成功，共{len(stocks)}只股票")
            return stocks
            
        except Exception as e:
            self.log_error("获取股票列表", e)
            return []
    
    async def get_stock_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取股票基本信息"""
        if not self.enabled:
            return None
        
        try:
            ts_code = self._convert_stock_code(stock_code)
            
            # 获取股票基本信息
            df = await self._run_in_executor(
                self.pro.stock_basic,
                ts_code=ts_code,
                fields="ts_code,symbol,name,fullname,industry,market,list_date"
            )
            
            if df.empty:
                return None
            
            row = df.iloc[0]
            
            stock_info = {
                "code": row["symbol"],
                "name": row["name"],
                "full_name": row["fullname"],
                "industry": row["industry"],
                "market": row["market"],
                "list_date": pd.to_datetime(row["list_date"]).date() if row["list_date"] else None,
            }
            
            return stock_info
            
        except Exception as e:
            self.log_error(f"获取股票信息({stock_code})", e)
            return None
    
    async def get_stock_price(
        self, 
        stock_code: str, 
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """获取股票价格数据"""
        if not self.enabled:
            return []
        
        try:
            ts_code = self._convert_stock_code(stock_code)
            
            # 设置默认日期
            if not end_date:
                end_date = date.today()
            if not start_date:
                start_date = date(end_date.year - 1, end_date.month, end_date.day)
            
            # 获取股票日线数据
            df = await self._run_in_executor(
                self.pro.daily,
                ts_code=ts_code,
                start_date=start_date.strftime("%Y%m%d"),
                end_date=end_date.strftime("%Y%m%d")
            )
            
            if df.empty:
                return []
            
            prices = []
            for _, row in df.iterrows():
                price = {
                    "stock_code": stock_code,
                    "trade_date": pd.to_datetime(row["trade_date"]).date(),
                    "open_price": float(row["open"]),
                    "high_price": float(row["high"]),
                    "low_price": float(row["low"]),
                    "close_price": float(row["close"]),
                    "pre_close": float(row["pre_close"]),
                    "volume": float(row["vol"]) * 100,  # 转换为手
                    "amount": float(row["amount"]),
                    "change": float(row["change"]),
                    "pct_change": float(row["pct_chg"]),
                }
                prices.append(price)
            
            self.log_info(f"获取股票价格数据成功({stock_code})，共{len(prices)}条")
            return prices
            
        except Exception as e:
            self.log_error(f"获取股票价格数据({stock_code})", e)
            return []
    
    async def get_realtime_price(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取实时价格"""
        if not self.enabled:
            return None
        
        try:
            # Tushare的实时数据需要高级权限，这里使用最新日线数据代替
            ts_code = self._convert_stock_code(stock_code)
            
            # 获取最新交易日数据
            df = await self._run_in_executor(
                self.pro.daily,
                ts_code=ts_code,
                start_date="",
                end_date="",
                limit=1
            )
            
            if df.empty:
                return None
            
            row = df.iloc[0]
            
            realtime = {
                "stock_code": stock_code,
                "current_price": float(row["close"]),
                "pre_close": float(row["pre_close"]),
                "change": float(row["change"]),
                "pct_change": float(row["pct_chg"]),
                "volume": float(row["vol"]) * 100,
                "amount": float(row["amount"]),
                "update_time": datetime.now(),
            }
            
            return realtime
            
        except Exception as e:
            self.log_error(f"获取实时价格({stock_code})", e)
            return None
    
    async def get_market_indices(self) -> List[Dict[str, Any]]:
        """获取市场指数"""
        if not self.enabled:
            return []
        
        try:
            # 获取主要指数的最新数据
            index_codes = ["000001.SH", "399001.SZ", "399006.SZ"]
            
            indices = []
            for ts_code in index_codes:
                df = await self._run_in_executor(
                    self.pro.index_daily,
                    ts_code=ts_code,
                    start_date="",
                    end_date="",
                    limit=1
                )
                
                if not df.empty:
                    row = df.iloc[0]
                    index = {
                        "code": ts_code.split(".")[0],
                        "name": self._get_index_name(ts_code),
                        "close_price": float(row["close"]),
                        "change": float(row["change"]),
                        "pct_change": float(row["pct_chg"]),
                        "volume": float(row["vol"]),
                        "amount": float(row["amount"]),
                        "trade_date": pd.to_datetime(row["trade_date"]).date(),
                    }
                    indices.append(index)
            
            self.log_info(f"获取市场指数成功，共{len(indices)}个")
            return indices
            
        except Exception as e:
            self.log_error("获取市场指数", e)
            return []
    
    def _get_index_name(self, ts_code: str) -> str:
        """获取指数名称"""
        index_names = {
            "000001.SH": "上证指数",
            "399001.SZ": "深证成指",
            "399006.SZ": "创业板指",
        }
        return index_names.get(ts_code, ts_code)
