"""
股票数据仓储
"""

from datetime import date
from typing import Optional, Sequence
from sqlalchemy import select, and_, or_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.stock import Stock, StockPrice, StockRealtime
from app.repositories.base import BaseRepository


class StockRepository(BaseRepository[Stock]):
    """股票信息仓储"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Stock, db)
    
    async def search_stocks(
        self,
        keyword: Optional[str] = None,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        is_active: bool = True,
        skip: int = 0,
        limit: int = 100,
    ) -> tuple[Sequence[Stock], int]:
        """搜索股票"""
        query = select(Stock)
        count_query = select(func.count(Stock.id))
        
        # 构建过滤条件
        conditions = [Stock.is_active == is_active]
        
        if keyword:
            keyword_condition = or_(
                Stock.code.ilike(f"%{keyword}%"),
                Stock.name.ilike(f"%{keyword}%"),
            )
            conditions.append(keyword_condition)
        
        if market:
            conditions.append(Stock.market == market)
        
        if industry:
            conditions.append(Stock.industry == industry)
        
        # 应用过滤条件
        if conditions:
            query = query.where(and_(*conditions))
            count_query = count_query.where(and_(*conditions))
        
        # 获取总数
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        # 获取数据
        query = query.offset(skip).limit(limit).order_by(Stock.code)
        result = await self.db.execute(query)
        stocks = result.scalars().all()
        
        return stocks, total
    
    async def get_by_code(self, code: str) -> Optional[Stock]:
        """根据股票代码获取股票信息"""
        result = await self.db.execute(
            select(Stock).where(Stock.code == code)
        )
        return result.scalar_one_or_none()
    
    async def get_by_codes(self, codes: list[str]) -> Sequence[Stock]:
        """根据股票代码列表获取股票信息"""
        result = await self.db.execute(
            select(Stock).where(Stock.code.in_(codes))
        )
        return result.scalars().all()


class StockPriceRepository(BaseRepository[StockPrice]):
    """股票价格仓储"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(StockPrice, db)
    
    async def get_price_history(
        self,
        stock_code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100,
    ) -> Sequence[StockPrice]:
        """获取股票价格历史"""
        query = select(StockPrice).where(StockPrice.stock_code == stock_code)
        
        if start_date:
            query = query.where(StockPrice.trade_date >= start_date)
        
        if end_date:
            query = query.where(StockPrice.trade_date <= end_date)
        
        query = query.order_by(desc(StockPrice.trade_date)).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_latest_price(self, stock_code: str) -> Optional[StockPrice]:
        """获取最新价格"""
        result = await self.db.execute(
            select(StockPrice)
            .where(StockPrice.stock_code == stock_code)
            .order_by(desc(StockPrice.trade_date))
            .limit(1)
        )
        return result.scalar_one_or_none()
    
    async def get_prices_by_date(self, trade_date: date) -> Sequence[StockPrice]:
        """获取指定日期的所有股票价格"""
        result = await self.db.execute(
            select(StockPrice).where(StockPrice.trade_date == trade_date)
        )
        return result.scalars().all()


class StockRealtimeRepository(BaseRepository[StockRealtime]):
    """股票实时数据仓储"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(StockRealtime, db)
    
    async def get_by_code(self, stock_code: str) -> Optional[StockRealtime]:
        """根据股票代码获取实时数据"""
        result = await self.db.execute(
            select(StockRealtime).where(StockRealtime.stock_code == stock_code)
        )
        return result.scalar_one_or_none()
    
    async def get_by_codes(self, stock_codes: list[str]) -> Sequence[StockRealtime]:
        """根据股票代码列表获取实时数据"""
        result = await self.db.execute(
            select(StockRealtime).where(StockRealtime.stock_code.in_(stock_codes))
        )
        return result.scalars().all()
    
    async def upsert_realtime_data(self, data: dict) -> StockRealtime:
        """插入或更新实时数据"""
        existing = await self.get_by_code(data["stock_code"])
        
        if existing:
            # 更新现有数据
            for key, value in data.items():
                if hasattr(existing, key):
                    setattr(existing, key, value)
            await self.db.commit()
            await self.db.refresh(existing)
            return existing
        else:
            # 创建新数据
            return await self.create(data)
