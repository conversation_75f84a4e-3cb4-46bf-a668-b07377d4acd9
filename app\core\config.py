"""
应用配置管理
"""

from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = Field(default="量化交易系统", description="应用名称")
    app_version: str = Field(default="0.1.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    secret_key: str = Field(default="dev-secret-key-change-in-production", description="应用密钥")
    allowed_hosts: List[str] = Field(default=["localhost", "127.0.0.1"], description="允许的主机")

    # 数据库配置
    database_url: str = Field(default="sqlite+aiosqlite:///./quantitative.db", description="数据库连接URL")
    database_echo: bool = Field(default=False, description="数据库SQL日志")
    
    # Redis配置
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis连接URL")
    redis_cache_ttl: int = Field(default=3600, description="Redis缓存TTL")
    
    # 外部数据源配置
    tushare_token: Optional[str] = Field(default=None, description="Tushare API Token")
    akshare_enabled: bool = Field(default=True, description="是否启用AKShare")
    
    # Celery配置
    celery_broker_url: str = Field(default="redis://localhost:6379/1", description="Celery Broker URL")
    celery_result_backend: str = Field(default="redis://localhost:6379/2", description="Celery Result Backend")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式")
    
    # API配置
    api_v1_prefix: str = Field(default="/api/v1", description="API v1前缀")
    cors_origins: str = Field(
        default='["http://localhost:3000", "http://localhost:8080"]',
        description="CORS允许的源"
    )

    @property
    def cors_origins_list(self) -> List[str]:
        """解析CORS源列表"""
        try:
            import json
            return json.loads(self.cors_origins)
        except:
            return ["http://localhost:3000", "http://localhost:8080"]
    
    # 安全配置
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间(分钟)")
    refresh_token_expire_days: int = Field(default=7, description="刷新令牌过期时间(天)")
    algorithm: str = Field(default="HS256", description="JWT算法")
    
    # 数据更新配置
    data_update_interval: int = Field(default=300, description="数据更新间隔(秒)")
    realtime_update_interval: int = Field(default=5, description="实时数据更新间隔(秒)")
    
    # 缓存配置
    cache_stock_info_ttl: int = Field(default=86400, description="股票信息缓存TTL(秒)")
    cache_realtime_data_ttl: int = Field(default=10, description="实时数据缓存TTL(秒)")
    cache_market_data_ttl: int = Field(default=3600, description="市场数据缓存TTL(秒)")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()
