"""
股票相关API端点
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.schemas.stock import (
    StockInfo, StockQuery, StockDetail, StockPrice, 
    StockPriceQuery, BatchStockQuery
)
from app.schemas.response import BaseResponse, PaginationResponse
from app.repositories.stock_repository import (
    StockRepository, StockPriceRepository, StockRealtimeRepository
)
from app.api.deps import (
    get_stock_repository, 
    get_stock_price_repository,
    get_stock_realtime_repository
)

router = APIRouter()


@router.get("/", response_model=BaseResponse[PaginationResponse[StockInfo]])
async def get_stocks(
    query: StockQuery = Depends(),
    stock_repo: StockRepository = Depends(get_stock_repository),
):
    """
    获取股票列表
    
    - **keyword**: 关键词搜索(股票代码或名称)
    - **market**: 交易市场筛选(SH/SZ)
    - **industry**: 行业筛选
    - **is_active**: 是否活跃股票
    - **page**: 页码
    - **size**: 每页大小
    """
    try:
        skip = (query.page - 1) * query.size
        
        stocks, total = await stock_repo.search_stocks(
            keyword=query.keyword,
            market=query.market,
            industry=query.industry,
            is_active=query.is_active,
            skip=skip,
            limit=query.size,
        )
        
        # 转换为响应模式
        stock_infos = [StockInfo.model_validate(stock) for stock in stocks]
        
        pagination = PaginationResponse.create(
            items=stock_infos,
            total=total,
            page=query.page,
            size=query.size,
        )
        
        return BaseResponse.success(pagination)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")


@router.get("/{stock_code}", response_model=BaseResponse[StockDetail])
async def get_stock_detail(
    stock_code: str,
    stock_repo: StockRepository = Depends(get_stock_repository),
    price_repo: StockPriceRepository = Depends(get_stock_price_repository),
    realtime_repo: StockRealtimeRepository = Depends(get_stock_realtime_repository),
):
    """
    获取股票详细信息
    
    - **stock_code**: 股票代码
    """
    try:
        # 获取股票基本信息
        stock = await stock_repo.get_by_code(stock_code)
        if not stock:
            raise HTTPException(status_code=404, detail="股票不存在")
        
        # 获取实时价格
        realtime_data = await realtime_repo.get_by_code(stock_code)
        
        # 获取近期价格(最近30天)
        recent_prices = await price_repo.get_price_history(
            stock_code=stock_code,
            limit=30
        )
        
        # 构建详细信息
        stock_detail = StockDetail(
            **stock.to_dict(),
            latest_price=realtime_data,
            recent_prices=[StockPrice.model_validate(price) for price in recent_prices]
        )
        
        return BaseResponse.success(stock_detail)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票详情失败: {str(e)}")


@router.get("/{stock_code}/prices", response_model=BaseResponse[List[StockPrice]])
async def get_stock_prices(
    stock_code: str,
    query: StockPriceQuery = Depends(),
    price_repo: StockPriceRepository = Depends(get_stock_price_repository),
):
    """
    获取股票价格历史
    
    - **stock_code**: 股票代码
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - **limit**: 限制数量
    """
    try:
        prices = await price_repo.get_price_history(
            stock_code=stock_code,
            start_date=query.start_date,
            end_date=query.end_date,
            limit=query.limit,
        )
        
        price_list = [StockPrice.model_validate(price) for price in prices]
        
        return BaseResponse.success(price_list)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取价格历史失败: {str(e)}")


@router.post("/batch", response_model=BaseResponse[List[StockInfo]])
async def get_stocks_batch(
    query: BatchStockQuery,
    stock_repo: StockRepository = Depends(get_stock_repository),
):
    """
    批量获取股票信息
    
    - **stock_codes**: 股票代码列表(最多100个)
    """
    try:
        if len(query.stock_codes) > 100:
            raise HTTPException(status_code=400, detail="股票代码数量不能超过100个")
        
        stocks = await stock_repo.get_by_codes(query.stock_codes)
        
        stock_infos = [StockInfo.model_validate(stock) for stock in stocks]
        
        return BaseResponse.success(stock_infos)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量获取股票信息失败: {str(e)}")


@router.get("/search/suggest")
async def search_suggest(
    q: str = Query(..., description="搜索关键词", min_length=1),
    limit: int = Query(10, description="建议数量", ge=1, le=20),
    stock_repo: StockRepository = Depends(get_stock_repository),
):
    """
    股票搜索建议
    
    - **q**: 搜索关键词
    - **limit**: 建议数量
    """
    try:
        stocks, _ = await stock_repo.search_stocks(
            keyword=q,
            is_active=True,
            skip=0,
            limit=limit,
        )
        
        suggestions = [
            {
                "code": stock.code,
                "name": stock.name,
                "market": stock.market,
                "industry": stock.industry,
            }
            for stock in stocks
        ]
        
        return BaseResponse.success(suggestions)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索建议失败: {str(e)}")
