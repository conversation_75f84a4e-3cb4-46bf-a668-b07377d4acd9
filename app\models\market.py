"""
市场数据相关模型
"""

from datetime import date
from decimal import Decimal
from typing import Optional
from sqlalchemy import String, Numeric, Date, Index
from sqlalchemy.orm import Mapped, mapped_column

from app.models.base import Base, TimestampMixin


class MarketIndex(Base, TimestampMixin):
    """市场指数表"""
    
    __tablename__ = "market_indices"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="主键ID")
    
    # 指数代码和名称
    code: Mapped[str] = mapped_column(String(10), index=True, comment="指数代码")
    name: Mapped[str] = mapped_column(String(100), comment="指数名称")
    
    # 交易日期
    trade_date: Mapped[date] = mapped_column(Date, index=True, comment="交易日期")
    
    # 指数数据
    open_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="开盘点数"
    )
    high_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="最高点数"
    )
    low_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="最低点数"
    )
    close_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="收盘点数"
    )
    pre_close: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="前收盘点数"
    )
    
    # 涨跌信息
    change: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="涨跌点数"
    )
    pct_change: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="涨跌幅(%)"
    )
    
    # 成交信息
    volume: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2), comment="成交量(万手)"
    )
    amount: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2), comment="成交额(亿元)"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_index_code_date", "code", "trade_date"),
        Index("idx_index_date", "trade_date"),
    )


class Industry(Base, TimestampMixin):
    """行业板块表"""
    
    __tablename__ = "industries"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="主键ID")
    
    # 行业信息
    code: Mapped[str] = mapped_column(String(20), unique=True, index=True, comment="行业代码")
    name: Mapped[str] = mapped_column(String(100), comment="行业名称")
    level: Mapped[int] = mapped_column(comment="行业级别(1:一级行业, 2:二级行业)")
    parent_code: Mapped[Optional[str]] = mapped_column(String(20), comment="父级行业代码")
    
    # 统计信息
    stock_count: Mapped[int] = mapped_column(default=0, comment="股票数量")
    
    # 索引
    __table_args__ = (
        Index("idx_industry_level", "level"),
        Index("idx_industry_parent", "parent_code"),
    )


class IndustryDaily(Base, TimestampMixin):
    """行业日线数据表"""
    
    __tablename__ = "industry_daily"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="主键ID")
    
    # 行业代码
    industry_code: Mapped[str] = mapped_column(String(20), index=True, comment="行业代码")
    
    # 交易日期
    trade_date: Mapped[date] = mapped_column(Date, index=True, comment="交易日期")
    
    # 涨跌统计
    up_count: Mapped[int] = mapped_column(default=0, comment="上涨股票数")
    down_count: Mapped[int] = mapped_column(default=0, comment="下跌股票数")
    flat_count: Mapped[int] = mapped_column(default=0, comment="平盘股票数")
    
    # 平均涨跌幅
    avg_pct_change: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="平均涨跌幅(%)"
    )
    
    # 成交统计
    total_volume: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2), comment="总成交量(万手)"
    )
    total_amount: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2), comment="总成交额(亿元)"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_industry_daily_code_date", "industry_code", "trade_date"),
        Index("idx_industry_daily_date", "trade_date"),
    )


class TradingCalendar(Base):
    """交易日历表"""
    
    __tablename__ = "trading_calendar"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="主键ID")
    
    # 日期
    cal_date: Mapped[date] = mapped_column(Date, unique=True, index=True, comment="日期")
    
    # 是否交易日
    is_open: Mapped[bool] = mapped_column(comment="是否交易日")
    
    # 前一个交易日
    pretrade_date: Mapped[Optional[date]] = mapped_column(Date, comment="前一个交易日")
    
    # 索引
    __table_args__ = (
        Index("idx_calendar_open", "is_open"),
    )
