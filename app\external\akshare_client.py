"""
AKShare数据源客户端
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import date, datetime
import pandas as pd

from app.external.base import BaseDataSource
from app.core.config import settings


class AKShareClient(BaseDataSource):
    """AKShare数据源客户端"""
    
    def __init__(self):
        super().__init__("akshare")
        self.enabled = settings.akshare_enabled
        
        if self.enabled:
            try:
                import akshare as ak
                self.ak = ak
                self.log_info("AKShare客户端初始化成功")
            except ImportError:
                self.log_error("初始化", Exception("AKShare未安装"))
                self.enabled = False
        else:
            self.log_info("AKShare客户端已禁用")
    
    async def _run_in_executor(self, func, *args, **kwargs):
        """在线程池中运行同步函数"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, func, *args, **kwargs)
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        if not self.enabled:
            return []
        
        try:
            # 获取A股股票列表
            df = await self._run_in_executor(self.ak.stock_info_a_code_name)
            
            stocks = []
            for _, row in df.iterrows():
                stock = {
                    "code": row["code"],
                    "name": row["name"],
                    "market": "SH" if row["code"].startswith("6") else "SZ",
                }
                stocks.append(stock)
            
            self.log_info(f"获取股票列表成功，共{len(stocks)}只股票")
            return stocks
            
        except Exception as e:
            self.log_error("获取股票列表", e)
            return []
    
    async def get_stock_info(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取股票基本信息"""
        if not self.enabled:
            return None
        
        try:
            # 获取股票基本信息
            df = await self._run_in_executor(
                self.ak.stock_individual_info_em, 
                symbol=stock_code
            )
            
            if df.empty:
                return None
            
            # 转换为字典格式
            info = {}
            for _, row in df.iterrows():
                info[row["item"]] = row["value"]
            
            # 标准化字段名
            stock_info = {
                "code": stock_code,
                "name": info.get("股票简称", ""),
                "full_name": info.get("股票全称", ""),
                "industry": info.get("所属行业", ""),
                "market": "SH" if stock_code.startswith("6") else "SZ",
                "list_date": self._parse_date(info.get("上市时间")),
                "total_share": self._parse_number(info.get("总股本")),
                "float_share": self._parse_number(info.get("流通股本")),
            }
            
            return stock_info
            
        except Exception as e:
            self.log_error(f"获取股票信息({stock_code})", e)
            return None
    
    async def get_stock_price(
        self, 
        stock_code: str, 
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """获取股票价格数据"""
        if not self.enabled:
            return []
        
        try:
            # 设置默认日期
            if not end_date:
                end_date = date.today()
            if not start_date:
                start_date = date(end_date.year - 1, end_date.month, end_date.day)
            
            # 获取股票历史数据
            df = await self._run_in_executor(
                self.ak.stock_zh_a_hist,
                symbol=stock_code,
                period="daily",
                start_date=start_date.strftime("%Y%m%d"),
                end_date=end_date.strftime("%Y%m%d"),
                adjust=""
            )
            
            if df.empty:
                return []
            
            prices = []
            for _, row in df.iterrows():
                price = {
                    "stock_code": stock_code,
                    "trade_date": pd.to_datetime(row["日期"]).date(),
                    "open_price": float(row["开盘"]),
                    "high_price": float(row["最高"]),
                    "low_price": float(row["最低"]),
                    "close_price": float(row["收盘"]),
                    "volume": float(row["成交量"]),
                    "amount": float(row["成交额"]),
                    "pct_change": float(row["涨跌幅"]) if "涨跌幅" in row else None,
                }
                prices.append(price)
            
            self.log_info(f"获取股票价格数据成功({stock_code})，共{len(prices)}条")
            return prices
            
        except Exception as e:
            self.log_error(f"获取股票价格数据({stock_code})", e)
            return []
    
    async def get_realtime_price(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取实时价格"""
        if not self.enabled:
            return None
        
        try:
            # 获取实时行情
            df = await self._run_in_executor(
                self.ak.stock_zh_a_spot_em
            )
            
            # 查找指定股票
            stock_data = df[df["代码"] == stock_code]
            if stock_data.empty:
                return None
            
            row = stock_data.iloc[0]
            
            realtime = {
                "stock_code": stock_code,
                "current_price": float(row["最新价"]),
                "pre_close": float(row["昨收"]),
                "change": float(row["涨跌额"]),
                "pct_change": float(row["涨跌幅"]),
                "volume": float(row["成交量"]),
                "amount": float(row["成交额"]),
                "update_time": datetime.now(),
            }
            
            return realtime
            
        except Exception as e:
            self.log_error(f"获取实时价格({stock_code})", e)
            return None
    
    async def get_market_indices(self) -> List[Dict[str, Any]]:
        """获取市场指数"""
        if not self.enabled:
            return []
        
        try:
            # 获取主要指数实时数据
            df = await self._run_in_executor(self.ak.stock_zh_index_spot_em)
            
            # 筛选主要指数
            main_indices = ["000001", "399001", "399006"]  # 上证指数、深证成指、创业板指
            
            indices = []
            for _, row in df.iterrows():
                if row["代码"] in main_indices:
                    index = {
                        "code": row["代码"],
                        "name": row["名称"],
                        "close_price": float(row["最新价"]),
                        "change": float(row["涨跌额"]),
                        "pct_change": float(row["涨跌幅"]),
                        "volume": float(row["成交量"]),
                        "amount": float(row["成交额"]),
                        "trade_date": date.today(),
                    }
                    indices.append(index)
            
            self.log_info(f"获取市场指数成功，共{len(indices)}个")
            return indices
            
        except Exception as e:
            self.log_error("获取市场指数", e)
            return []
    
    def _parse_date(self, date_str: str) -> Optional[date]:
        """解析日期字符串"""
        if not date_str:
            return None
        
        try:
            return pd.to_datetime(date_str).date()
        except:
            return None
    
    def _parse_number(self, num_str: str) -> Optional[float]:
        """解析数字字符串"""
        if not num_str:
            return None
        
        try:
            # 移除单位(万、亿等)
            num_str = str(num_str).replace("万", "").replace("亿", "")
            return float(num_str)
        except:
            return None
