"""
基础仓储类
"""

from typing import Any, Generic, TypeVar, Optional, Sequence
from sqlalchemy import select, func, delete, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase

ModelType = TypeVar("ModelType", bound=DeclarativeBase)


class BaseRepository(Generic[ModelType]):
    """基础仓储类"""
    
    def __init__(self, model: type[ModelType], db: AsyncSession):
        self.model = model
        self.db = db
    
    async def get(self, id: Any) -> Optional[ModelType]:
        """根据ID获取单个对象"""
        result = await self.db.execute(
            select(self.model).where(self.model.id == id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_field(self, field: str, value: Any) -> Optional[ModelType]:
        """根据字段获取单个对象"""
        result = await self.db.execute(
            select(self.model).where(getattr(self.model, field) == value)
        )
        return result.scalar_one_or_none()
    
    async def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        **filters
    ) -> Sequence[ModelType]:
        """获取多个对象"""
        query = select(self.model)
        
        # 添加过滤条件
        for field, value in filters.items():
            if hasattr(self.model, field) and value is not None:
                query = query.where(getattr(self.model, field) == value)
        
        query = query.offset(skip).limit(limit)
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def count(self, **filters) -> int:
        """获取对象数量"""
        query = select(func.count(self.model.id))
        
        # 添加过滤条件
        for field, value in filters.items():
            if hasattr(self.model, field) and value is not None:
                query = query.where(getattr(self.model, field) == value)
        
        result = await self.db.execute(query)
        return result.scalar() or 0
    
    async def create(self, obj_in: dict[str, Any]) -> ModelType:
        """创建对象"""
        db_obj = self.model(**obj_in)
        self.db.add(db_obj)
        await self.db.commit()
        await self.db.refresh(db_obj)
        return db_obj
    
    async def update(self, id: Any, obj_in: dict[str, Any]) -> Optional[ModelType]:
        """更新对象"""
        await self.db.execute(
            update(self.model)
            .where(self.model.id == id)
            .values(**obj_in)
        )
        await self.db.commit()
        return await self.get(id)
    
    async def delete(self, id: Any) -> bool:
        """删除对象"""
        result = await self.db.execute(
            delete(self.model).where(self.model.id == id)
        )
        await self.db.commit()
        return result.rowcount > 0
    
    async def bulk_create(self, objs_in: list[dict[str, Any]]) -> list[ModelType]:
        """批量创建对象"""
        db_objs = [self.model(**obj_in) for obj_in in objs_in]
        self.db.add_all(db_objs)
        await self.db.commit()
        for db_obj in db_objs:
            await self.db.refresh(db_obj)
        return db_objs
    
    async def exists(self, **filters) -> bool:
        """检查对象是否存在"""
        query = select(func.count(self.model.id))
        
        # 添加过滤条件
        for field, value in filters.items():
            if hasattr(self.model, field) and value is not None:
                query = query.where(getattr(self.model, field) == value)
        
        result = await self.db.execute(query)
        count = result.scalar() or 0
        return count > 0
