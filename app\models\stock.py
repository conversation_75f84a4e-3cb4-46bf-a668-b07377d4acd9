"""
股票相关数据模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from sqlalchemy import String, Text, Numeric, Date, DateTime, Boolean, Integer, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import Base, TimestampMixin


class Stock(Base, TimestampMixin):
    """股票基本信息表"""
    
    __tablename__ = "stocks"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="主键ID")
    
    # 股票代码和名称
    code: Mapped[str] = mapped_column(String(10), unique=True, index=True, comment="股票代码")
    name: Mapped[str] = mapped_column(String(100), comment="股票名称")
    
    # 基本信息
    market: Mapped[str] = mapped_column(String(10), comment="交易市场(SH/SZ)")
    industry: Mapped[Optional[str]] = mapped_column(String(50), comment="所属行业")
    sector: Mapped[Optional[str]] = mapped_column(String(50), comment="所属板块")
    
    # 公司信息
    full_name: Mapped[Optional[str]] = mapped_column(String(200), comment="公司全称")
    english_name: Mapped[Optional[str]] = mapped_column(String(200), comment="英文名称")
    
    # 上市信息
    list_date: Mapped[Optional[date]] = mapped_column(Date, comment="上市日期")
    delist_date: Mapped[Optional[date]] = mapped_column(Date, comment="退市日期")
    
    # 状态信息
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否活跃")
    is_st: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否ST股票")
    
    # 财务指标
    total_share: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2), comment="总股本(万股)"
    )
    float_share: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2), comment="流通股本(万股)"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_stock_market", "market"),
        Index("idx_stock_industry", "industry"),
        Index("idx_stock_active", "is_active"),
    )


class StockPrice(Base, TimestampMixin):
    """股票价格数据表"""
    
    __tablename__ = "stock_prices"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="主键ID")
    
    # 股票代码
    stock_code: Mapped[str] = mapped_column(String(10), index=True, comment="股票代码")
    
    # 交易日期
    trade_date: Mapped[date] = mapped_column(Date, index=True, comment="交易日期")
    
    # 价格信息
    open_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="开盘价"
    )
    high_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="最高价"
    )
    low_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="最低价"
    )
    close_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="收盘价"
    )
    pre_close: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="前收盘价"
    )
    
    # 成交信息
    volume: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2), comment="成交量(手)"
    )
    amount: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2), comment="成交额(千元)"
    )
    
    # 涨跌信息
    change: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="涨跌额"
    )
    pct_change: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="涨跌幅(%)"
    )
    
    # 索引
    __table_args__ = (
        Index("idx_price_stock_date", "stock_code", "trade_date"),
        Index("idx_price_date", "trade_date"),
    )


class StockRealtime(Base):
    """股票实时数据表"""
    
    __tablename__ = "stock_realtime"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="主键ID")
    
    # 股票代码
    stock_code: Mapped[str] = mapped_column(String(10), unique=True, index=True, comment="股票代码")
    
    # 实时价格
    current_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="当前价格"
    )
    pre_close: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="前收盘价"
    )
    
    # 涨跌信息
    change: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="涨跌额"
    )
    pct_change: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3), comment="涨跌幅(%)"
    )
    
    # 成交信息
    volume: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2), comment="成交量(手)"
    )
    amount: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2), comment="成交额(千元)"
    )
    
    # 买卖盘信息
    bid1: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="买一价")
    bid1_volume: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="买一量")
    ask1: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="卖一价")
    ask1_volume: Mapped[Optional[Decimal]] = mapped_column(Numeric(20, 2), comment="卖一量")
    
    # 更新时间
    update_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), comment="更新时间"
    )
