# 应用配置
APP_NAME=量化交易系统
APP_VERSION=0.1.0
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/quantitative_db
DATABASE_ECHO=False

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_CACHE_TTL=3600

# 外部数据源配置
TUSHARE_TOKEN=your-tushare-token-here
AKSHARE_ENABLED=True

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# API配置
API_V1_PREFIX=/api/v1
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# 安全配置
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256

# 数据更新配置
DATA_UPDATE_INTERVAL=300  # 5分钟
REALTIME_UPDATE_INTERVAL=5  # 5秒

# 缓存配置
CACHE_STOCK_INFO_TTL=86400  # 24小时
CACHE_REALTIME_DATA_TTL=10  # 10秒
CACHE_MARKET_DATA_TTL=3600  # 1小时
