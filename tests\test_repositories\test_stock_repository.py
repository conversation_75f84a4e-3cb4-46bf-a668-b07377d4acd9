"""
股票仓储测试
"""

import pytest
from datetime import date
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.stock_repository import StockRepository, StockPriceRepository, StockRealtimeRepository
from app.models.stock import Stock, StockPrice, StockRealtime


class TestStockRepository:
    """股票仓储测试类"""
    
    @pytest.mark.asyncio
    async def test_create_stock(self, db_session: AsyncSession, sample_stock_data: dict):
        """测试创建股票"""
        repo = StockRepository(db_session)
        
        stock = await repo.create(sample_stock_data)
        
        assert stock.id is not None
        assert stock.code == "000001"
        assert stock.name == "平安银行"
    
    @pytest.mark.asyncio
    async def test_get_stock_by_id(self, db_session: AsyncSession, sample_stock_data: dict):
        """测试根据ID获取股票"""
        repo = StockRepository(db_session)
        
        created_stock = await repo.create(sample_stock_data)
        retrieved_stock = await repo.get(created_stock.id)
        
        assert retrieved_stock is not None
        assert retrieved_stock.id == created_stock.id
        assert retrieved_stock.code == "000001"
    
    @pytest.mark.asyncio
    async def test_get_stock_by_code(self, db_session: AsyncSession, sample_stock_data: dict):
        """测试根据代码获取股票"""
        repo = StockRepository(db_session)
        
        await repo.create(sample_stock_data)
        stock = await repo.get_by_code("000001")
        
        assert stock is not None
        assert stock.code == "000001"
        assert stock.name == "平安银行"
    
    @pytest.mark.asyncio
    async def test_get_stock_by_codes(self, db_session: AsyncSession):
        """测试根据代码列表获取股票"""
        repo = StockRepository(db_session)
        
        # 创建多个股票
        stock_data_list = [
            {"code": "000001", "name": "平安银行", "market": "SZ", "is_active": True, "is_st": False},
            {"code": "000002", "name": "万科A", "market": "SZ", "is_active": True, "is_st": False},
            {"code": "600000", "name": "浦发银行", "market": "SH", "is_active": True, "is_st": False},
        ]
        
        for stock_data in stock_data_list:
            await repo.create(stock_data)
        
        stocks = await repo.get_by_codes(["000001", "000002", "999999"])
        
        assert len(stocks) == 2  # 只有000001和000002存在
        codes = [stock.code for stock in stocks]
        assert "000001" in codes
        assert "000002" in codes
    
    @pytest.mark.asyncio
    async def test_search_stocks(self, db_session: AsyncSession):
        """测试搜索股票"""
        repo = StockRepository(db_session)
        
        # 创建测试数据
        stock_data_list = [
            {"code": "000001", "name": "平安银行", "market": "SZ", "industry": "银行", "is_active": True, "is_st": False},
            {"code": "000002", "name": "万科A", "market": "SZ", "industry": "房地产", "is_active": True, "is_st": False},
            {"code": "600000", "name": "浦发银行", "market": "SH", "industry": "银行", "is_active": True, "is_st": False},
            {"code": "600001", "name": "邯郸钢铁", "market": "SH", "industry": "钢铁", "is_active": False, "is_st": True},
        ]
        
        for stock_data in stock_data_list:
            await repo.create(stock_data)
        
        # 测试无条件搜索
        stocks, total = await repo.search_stocks()
        assert total == 3  # 默认只返回活跃股票
        
        # 测试按关键词搜索
        stocks, total = await repo.search_stocks(keyword="银行")
        assert total == 2
        
        # 测试按市场搜索
        stocks, total = await repo.search_stocks(market="SH")
        assert total == 1  # 只有浦发银行是活跃的上海股票
        
        # 测试按行业搜索
        stocks, total = await repo.search_stocks(industry="银行")
        assert total == 2
        
        # 测试包含非活跃股票
        stocks, total = await repo.search_stocks(is_active=False)
        assert total == 1  # 邯郸钢铁
    
    @pytest.mark.asyncio
    async def test_update_stock(self, db_session: AsyncSession, sample_stock_data: dict):
        """测试更新股票"""
        repo = StockRepository(db_session)
        
        stock = await repo.create(sample_stock_data)
        updated_stock = await repo.update(stock.id, {"name": "平安银行更新"})
        
        assert updated_stock is not None
        assert updated_stock.name == "平安银行更新"
    
    @pytest.mark.asyncio
    async def test_delete_stock(self, db_session: AsyncSession, sample_stock_data: dict):
        """测试删除股票"""
        repo = StockRepository(db_session)
        
        stock = await repo.create(sample_stock_data)
        success = await repo.delete(stock.id)
        
        assert success is True
        
        deleted_stock = await repo.get(stock.id)
        assert deleted_stock is None


class TestStockPriceRepository:
    """股票价格仓储测试类"""
    
    @pytest.mark.asyncio
    async def test_create_price(self, db_session: AsyncSession, sample_price_data: dict):
        """测试创建价格数据"""
        repo = StockPriceRepository(db_session)
        
        price = await repo.create(sample_price_data)
        
        assert price.id is not None
        assert price.stock_code == "000001"
        assert price.close_price == 10.2
    
    @pytest.mark.asyncio
    async def test_get_price_history(self, db_session: AsyncSession):
        """测试获取价格历史"""
        repo = StockPriceRepository(db_session)
        
        # 创建多个价格数据
        price_data_list = [
            {
                "stock_code": "000001",
                "trade_date": date(2024, 1, 1),
                "close_price": 10.0,
                "open_price": 9.8,
                "high_price": 10.2,
                "low_price": 9.5,
                "volume": 1000000,
                "amount": 10000000,
            },
            {
                "stock_code": "000001",
                "trade_date": date(2024, 1, 2),
                "close_price": 10.5,
                "open_price": 10.0,
                "high_price": 10.8,
                "low_price": 9.9,
                "volume": 1200000,
                "amount": 12500000,
            },
        ]
        
        for price_data in price_data_list:
            await repo.create(price_data)
        
        # 获取价格历史
        prices = await repo.get_price_history("000001")
        
        assert len(prices) == 2
        # 应该按日期降序排列
        assert prices[0].trade_date == date(2024, 1, 2)
        assert prices[1].trade_date == date(2024, 1, 1)
    
    @pytest.mark.asyncio
    async def test_get_latest_price(self, db_session: AsyncSession):
        """测试获取最新价格"""
        repo = StockPriceRepository(db_session)
        
        # 创建多个价格数据
        price_data_list = [
            {
                "stock_code": "000001",
                "trade_date": date(2024, 1, 1),
                "close_price": 10.0,
            },
            {
                "stock_code": "000001",
                "trade_date": date(2024, 1, 2),
                "close_price": 10.5,
            },
        ]
        
        for price_data in price_data_list:
            await repo.create(price_data)
        
        latest_price = await repo.get_latest_price("000001")
        
        assert latest_price is not None
        assert latest_price.trade_date == date(2024, 1, 2)
        assert latest_price.close_price == 10.5


class TestStockRealtimeRepository:
    """股票实时数据仓储测试类"""
    
    @pytest.mark.asyncio
    async def test_create_realtime(self, db_session: AsyncSession, sample_realtime_data: dict):
        """测试创建实时数据"""
        repo = StockRealtimeRepository(db_session)
        
        realtime = await repo.create(sample_realtime_data)
        
        assert realtime.id is not None
        assert realtime.stock_code == "000001"
        assert realtime.current_price == 10.2
    
    @pytest.mark.asyncio
    async def test_upsert_realtime_data(self, db_session: AsyncSession, sample_realtime_data: dict):
        """测试插入或更新实时数据"""
        repo = StockRealtimeRepository(db_session)
        
        # 第一次插入
        realtime1 = await repo.upsert_realtime_data(sample_realtime_data)
        assert realtime1.current_price == 10.2
        
        # 更新数据
        updated_data = sample_realtime_data.copy()
        updated_data["current_price"] = 11.0
        realtime2 = await repo.upsert_realtime_data(updated_data)
        
        assert realtime2.id == realtime1.id  # 应该是同一条记录
        assert realtime2.current_price == 11.0
