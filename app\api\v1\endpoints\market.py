"""
市场数据API端点
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query

from app.schemas.market import MarketIndex, Industry, IndustryDaily, MarketOverview
from app.schemas.response import BaseResponse
from app.repositories.market_repository import (
    MarketIndexRepository,
    IndustryRepository,
    IndustryDailyRepository,
)
from app.api.deps import (
    get_market_index_repository,
    get_industry_repository,
    get_industry_daily_repository,
)

router = APIRouter()


@router.get("/indices", response_model=BaseResponse[List[MarketIndex]])
async def get_market_indices(
    codes: str = Query(
        "000001,399001,399006", 
        description="指数代码，逗号分隔"
    ),
    index_repo: MarketIndexRepository = Depends(get_market_index_repository),
):
    """
    获取市场指数
    
    - **codes**: 指数代码列表，逗号分隔(默认：上证指数,深证成指,创业板指)
    """
    try:
        code_list = [code.strip() for code in codes.split(",") if code.strip()]
        
        if len(code_list) > 20:
            raise HTTPException(status_code=400, detail="指数代码数量不能超过20个")
        
        indices = await index_repo.get_latest_indices(code_list)
        
        index_infos = [MarketIndex.model_validate(index) for index in indices]
        
        return BaseResponse.success(index_infos)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取市场指数失败: {str(e)}")


@router.get("/indices/{code}/history", response_model=BaseResponse[List[MarketIndex]])
async def get_index_history(
    code: str,
    limit: int = Query(100, description="数量限制", ge=1, le=1000),
    index_repo: MarketIndexRepository = Depends(get_market_index_repository),
):
    """
    获取指数历史数据
    
    - **code**: 指数代码
    - **limit**: 数量限制
    """
    try:
        history = await index_repo.get_index_history(code=code, limit=limit)
        
        history_infos = [MarketIndex.model_validate(item) for item in history]
        
        return BaseResponse.success(history_infos)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取指数历史失败: {str(e)}")


@router.get("/industries", response_model=BaseResponse[List[Industry]])
async def get_industries(
    level: int = Query(1, description="行业级别", ge=1, le=2),
    industry_repo: IndustryRepository = Depends(get_industry_repository),
):
    """
    获取行业列表
    
    - **level**: 行业级别(1:一级行业, 2:二级行业)
    """
    try:
        industries = await industry_repo.get_by_level(level)
        
        industry_infos = [Industry.model_validate(industry) for industry in industries]
        
        return BaseResponse.success(industry_infos)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取行业列表失败: {str(e)}")


@router.get("/industries/{code}/children", response_model=BaseResponse[List[Industry]])
async def get_industry_children(
    code: str,
    industry_repo: IndustryRepository = Depends(get_industry_repository),
):
    """
    获取子行业
    
    - **code**: 父级行业代码
    """
    try:
        children = await industry_repo.get_children(code)
        
        children_infos = [Industry.model_validate(child) for child in children]
        
        return BaseResponse.success(children_infos)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取子行业失败: {str(e)}")


@router.get("/industries/hot", response_model=BaseResponse[List[IndustryDaily]])
async def get_hot_industries(
    limit: int = Query(20, description="数量限制", ge=1, le=50),
    industry_daily_repo: IndustryDailyRepository = Depends(get_industry_daily_repository),
):
    """
    获取热门行业
    
    - **limit**: 数量限制
    """
    try:
        hot_industries = await industry_daily_repo.get_latest_industry_data(limit)
        
        hot_industry_infos = [
            IndustryDaily.model_validate(industry) for industry in hot_industries
        ]
        
        return BaseResponse.success(hot_industry_infos)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门行业失败: {str(e)}")


@router.get("/overview", response_model=BaseResponse[MarketOverview])
async def get_market_overview(
    index_repo: MarketIndexRepository = Depends(get_market_index_repository),
    industry_daily_repo: IndustryDailyRepository = Depends(get_industry_daily_repository),
):
    """
    获取市场概览
    
    包含主要指数、热门行业、市场统计等信息
    """
    try:
        # 获取主要指数
        main_indices = await index_repo.get_latest_indices(
            ["000001", "399001", "399006"]  # 上证指数、深证成指、创业板指
        )
        
        # 获取热门行业
        hot_industries = await industry_daily_repo.get_latest_industry_data(10)
        
        # 构建市场统计(这里可以添加更多统计信息)
        market_stats = {
            "total_indices": len(main_indices),
            "hot_industries_count": len(hot_industries),
            "update_time": "2024-01-01T00:00:00Z",  # 实际应该是最新更新时间
        }
        
        overview = MarketOverview(
            indices=[MarketIndex.model_validate(index) for index in main_indices],
            hot_industries=[
                IndustryDaily.model_validate(industry) for industry in hot_industries
            ],
            market_stats=market_stats,
        )
        
        return BaseResponse.success(overview)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取市场概览失败: {str(e)}")
