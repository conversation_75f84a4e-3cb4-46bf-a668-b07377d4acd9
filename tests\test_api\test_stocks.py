"""
股票API测试
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.stock import Stock, StockPrice, StockRealtime


class TestStocksAPI:
    """股票API测试类"""
    
    @pytest.mark.asyncio
    async def test_get_stocks_empty(self, client: AsyncClient):
        """测试获取空股票列表"""
        response = await client.get("/api/v1/stocks/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["total"] == 0
        assert data["data"]["items"] == []
    
    @pytest.mark.asyncio
    async def test_get_stocks_with_data(
        self, 
        client: AsyncClient, 
        db_session: AsyncSession,
        sample_stock_data: dict
    ):
        """测试获取股票列表"""
        # 创建测试数据
        stock = Stock(**sample_stock_data)
        db_session.add(stock)
        await db_session.commit()
        
        response = await client.get("/api/v1/stocks/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["total"] == 1
        assert len(data["data"]["items"]) == 1
        assert data["data"]["items"][0]["code"] == "000001"
        assert data["data"]["items"][0]["name"] == "平安银行"
    
    @pytest.mark.asyncio
    async def test_search_stocks_by_keyword(
        self, 
        client: AsyncClient, 
        db_session: AsyncSession,
        sample_stock_data: dict
    ):
        """测试按关键词搜索股票"""
        # 创建测试数据
        stock = Stock(**sample_stock_data)
        db_session.add(stock)
        await db_session.commit()
        
        # 按代码搜索
        response = await client.get("/api/v1/stocks/?keyword=000001")
        assert response.status_code == 200
        
        data = response.json()
        assert data["data"]["total"] == 1
        
        # 按名称搜索
        response = await client.get("/api/v1/stocks/?keyword=平安")
        assert response.status_code == 200
        
        data = response.json()
        assert data["data"]["total"] == 1
    
    @pytest.mark.asyncio
    async def test_get_stock_detail_not_found(self, client: AsyncClient):
        """测试获取不存在的股票详情"""
        response = await client.get("/api/v1/stocks/999999")
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_get_stock_detail_success(
        self, 
        client: AsyncClient, 
        db_session: AsyncSession,
        sample_stock_data: dict,
        sample_realtime_data: dict
    ):
        """测试获取股票详情成功"""
        # 创建测试数据
        stock = Stock(**sample_stock_data)
        db_session.add(stock)
        
        realtime = StockRealtime(**sample_realtime_data)
        db_session.add(realtime)
        
        await db_session.commit()
        
        response = await client.get("/api/v1/stocks/000001")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["code"] == "000001"
        assert data["data"]["name"] == "平安银行"
        assert data["data"]["latest_price"] is not None
    
    @pytest.mark.asyncio
    async def test_get_stock_prices(
        self, 
        client: AsyncClient, 
        db_session: AsyncSession,
        sample_price_data: dict
    ):
        """测试获取股票价格历史"""
        # 创建测试数据
        price = StockPrice(**sample_price_data)
        db_session.add(price)
        await db_session.commit()
        
        response = await client.get("/api/v1/stocks/000001/prices")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert len(data["data"]) == 1
        assert data["data"][0]["stock_code"] == "000001"
    
    @pytest.mark.asyncio
    async def test_batch_get_stocks(
        self, 
        client: AsyncClient, 
        db_session: AsyncSession,
        sample_stock_data: dict
    ):
        """测试批量获取股票信息"""
        # 创建测试数据
        stock = Stock(**sample_stock_data)
        db_session.add(stock)
        await db_session.commit()
        
        response = await client.post(
            "/api/v1/stocks/batch",
            json={"stock_codes": ["000001", "000002"]}
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert len(data["data"]) == 1  # 只有000001存在
        assert data["data"][0]["code"] == "000001"
    
    @pytest.mark.asyncio
    async def test_batch_get_stocks_too_many(self, client: AsyncClient):
        """测试批量获取股票信息 - 数量超限"""
        stock_codes = [f"{i:06d}" for i in range(101)]  # 101个股票代码
        
        response = await client.post(
            "/api/v1/stocks/batch",
            json={"stock_codes": stock_codes}
        )
        assert response.status_code == 400
    
    @pytest.mark.asyncio
    async def test_search_suggest(
        self, 
        client: AsyncClient, 
        db_session: AsyncSession,
        sample_stock_data: dict
    ):
        """测试搜索建议"""
        # 创建测试数据
        stock = Stock(**sample_stock_data)
        db_session.add(stock)
        await db_session.commit()
        
        response = await client.get("/api/v1/stocks/search/suggest?q=平安")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert len(data["data"]) == 1
        assert data["data"][0]["code"] == "000001"
        assert data["data"][0]["name"] == "平安银行"
    
    @pytest.mark.asyncio
    async def test_pagination(
        self, 
        client: AsyncClient, 
        db_session: AsyncSession
    ):
        """测试分页功能"""
        # 创建多个测试股票
        stocks = []
        for i in range(25):
            stock_data = {
                "code": f"{i:06d}",
                "name": f"测试股票{i}",
                "market": "SZ",
                "is_active": True,
                "is_st": False,
            }
            stocks.append(Stock(**stock_data))
        
        db_session.add_all(stocks)
        await db_session.commit()
        
        # 测试第一页
        response = await client.get("/api/v1/stocks/?page=1&size=10")
        assert response.status_code == 200
        
        data = response.json()
        assert data["data"]["total"] == 25
        assert len(data["data"]["items"]) == 10
        assert data["data"]["page"] == 1
        assert data["data"]["size"] == 10
        assert data["data"]["pages"] == 3
        
        # 测试第二页
        response = await client.get("/api/v1/stocks/?page=2&size=10")
        assert response.status_code == 200
        
        data = response.json()
        assert data["data"]["page"] == 2
        assert len(data["data"]["items"]) == 10
