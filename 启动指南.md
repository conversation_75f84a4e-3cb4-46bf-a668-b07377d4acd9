# 量化交易系统启动指南

## 问题解决

您遇到的错误是因为缺少必需的环境变量配置。我已经为您创建了解决方案。

## 快速启动步骤

### 1. 安装依赖包

选择以下任一方式安装依赖：

**方式一：使用安装脚本（推荐）**
```bash
python install_deps.py
```

**方式二：使用pip直接安装**
```bash
pip install -r requirements.txt
```

**方式三：手动安装核心包**
```bash
pip install fastapi uvicorn sqlalchemy aiosqlite pydantic pydantic-settings python-dotenv
```

### 2. 启动应用

**方式一：简化启动（推荐用于测试）**
```bash
python simple_start.py
```

**方式二：完整启动**
```bash
python main.py
```

### 3. 访问应用

启动成功后，您可以访问：

- 主页: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## 配置说明

我已经为您创建了 `.env` 文件，包含了基本配置：

- 使用SQLite数据库（无需额外安装）
- 开启调试模式
- 配置了基本的安全密钥
- 设置了CORS允许的源

## 故障排除

### 如果遇到导入错误

1. 确保Python环境正确
2. 检查是否在正确的目录下运行
3. 尝试重新安装依赖包

### 如果端口被占用

修改启动脚本中的端口号，或者停止占用8000端口的其他程序。

### 如果需要使用PostgreSQL

1. 安装PostgreSQL数据库
2. 修改 `.env` 文件中的 `DATABASE_URL`
3. 安装 `asyncpg` 包：`pip install asyncpg`

## 下一步

1. 启动应用后，访问 http://localhost:8000/docs 查看API文档
2. 测试基本的API端点
3. 根据需要配置外部数据源（Tushare、AKShare）

## 技术支持

如果仍有问题，请检查：

1. Python版本（建议3.11+）
2. 网络连接（用于安装依赖包）
3. 防火墙设置（确保8000端口可访问）

祝您使用愉快！
