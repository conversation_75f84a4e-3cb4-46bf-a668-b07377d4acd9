"""
股票相关数据模式
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class StockBase(BaseModel):
    """股票基础模式"""
    
    code: str = Field(description="股票代码")
    name: str = Field(description="股票名称")
    market: str = Field(description="交易市场")


class StockInfo(StockBase):
    """股票信息模式"""
    
    industry: Optional[str] = Field(default=None, description="所属行业")
    sector: Optional[str] = Field(default=None, description="所属板块")
    full_name: Optional[str] = Field(default=None, description="公司全称")
    list_date: Optional[date] = Field(default=None, description="上市日期")
    is_active: bool = Field(description="是否活跃")
    is_st: bool = Field(description="是否ST股票")
    total_share: Optional[Decimal] = Field(default=None, description="总股本(万股)")
    float_share: Optional[Decimal] = Field(default=None, description="流通股本(万股)")
    
    model_config = ConfigDict(from_attributes=True)


class StockPrice(BaseModel):
    """股票价格模式"""
    
    stock_code: str = Field(description="股票代码")
    trade_date: date = Field(description="交易日期")
    open_price: Optional[Decimal] = Field(default=None, description="开盘价")
    high_price: Optional[Decimal] = Field(default=None, description="最高价")
    low_price: Optional[Decimal] = Field(default=None, description="最低价")
    close_price: Optional[Decimal] = Field(default=None, description="收盘价")
    pre_close: Optional[Decimal] = Field(default=None, description="前收盘价")
    volume: Optional[Decimal] = Field(default=None, description="成交量(手)")
    amount: Optional[Decimal] = Field(default=None, description="成交额(千元)")
    change: Optional[Decimal] = Field(default=None, description="涨跌额")
    pct_change: Optional[Decimal] = Field(default=None, description="涨跌幅(%)")
    
    model_config = ConfigDict(from_attributes=True)


class StockRealtime(BaseModel):
    """股票实时数据模式"""
    
    stock_code: str = Field(description="股票代码")
    current_price: Optional[Decimal] = Field(default=None, description="当前价格")
    pre_close: Optional[Decimal] = Field(default=None, description="前收盘价")
    change: Optional[Decimal] = Field(default=None, description="涨跌额")
    pct_change: Optional[Decimal] = Field(default=None, description="涨跌幅(%)")
    volume: Optional[Decimal] = Field(default=None, description="成交量(手)")
    amount: Optional[Decimal] = Field(default=None, description="成交额(千元)")
    bid1: Optional[Decimal] = Field(default=None, description="买一价")
    bid1_volume: Optional[Decimal] = Field(default=None, description="买一量")
    ask1: Optional[Decimal] = Field(default=None, description="卖一价")
    ask1_volume: Optional[Decimal] = Field(default=None, description="卖一量")
    update_time: datetime = Field(description="更新时间")
    
    model_config = ConfigDict(from_attributes=True)


class StockQuery(BaseModel):
    """股票查询参数"""
    
    keyword: Optional[str] = Field(default=None, description="关键词(代码或名称)")
    market: Optional[str] = Field(default=None, description="交易市场")
    industry: Optional[str] = Field(default=None, description="行业")
    is_active: Optional[bool] = Field(default=True, description="是否活跃")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")


class StockPriceQuery(BaseModel):
    """股票价格查询参数"""
    
    stock_code: str = Field(description="股票代码")
    start_date: Optional[date] = Field(default=None, description="开始日期")
    end_date: Optional[date] = Field(default=None, description="结束日期")
    limit: int = Field(default=100, ge=1, le=1000, description="限制数量")


class BatchStockQuery(BaseModel):
    """批量股票查询参数"""
    
    stock_codes: list[str] = Field(description="股票代码列表", max_length=100)


class StockDetail(StockInfo):
    """股票详细信息"""
    
    latest_price: Optional[StockRealtime] = Field(default=None, description="最新价格")
    recent_prices: list[StockPrice] = Field(default=[], description="近期价格")
    
    model_config = ConfigDict(from_attributes=True)
