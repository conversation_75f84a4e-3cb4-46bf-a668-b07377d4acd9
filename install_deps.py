"""
依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主函数"""
    print("开始安装量化交易系统依赖包...")
    
    # 基础依赖包
    packages = [
        "fastapi>=0.104.1",
        "uvicorn[standard]>=0.24.0",
        "sqlalchemy>=2.0.23",
        "alembic>=1.12.1",
        "aiosqlite>=0.19.0",
        "pydantic>=2.5.0",
        "pydantic-settings>=2.1.0",
        "python-multipart>=0.0.6",
        "structlog>=23.2.0",
        "httpx>=0.25.2",
        "pandas>=2.1.4",
        "numpy>=1.26.2",
        "python-dotenv>=1.0.0",
        "aioredis>=2.0.1",
        "redis>=5.0.1"
    ]
    
    failed_packages = []
    
    for package in packages:
        print(f"安装 {package}...")
        if install_package(package):
            print(f"✓ {package} 安装成功")
        else:
            print(f"✗ {package} 安装失败")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n以下包安装失败:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\n请手动安装这些包或检查网络连接")
        return False
    else:
        print("\n所有依赖包安装成功!")
        print("现在可以运行以下命令启动应用:")
        print("python main.py")
        print("或者")
        print("python simple_start.py")
        return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
