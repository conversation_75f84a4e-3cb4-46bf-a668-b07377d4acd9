"""
实时数据业务逻辑服务
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.stock_repository import StockRealtimeRepository
from app.external.akshare_client import AKShare<PERSON>lient
from app.external.tushare_client import Tushare<PERSON><PERSON>
from app.schemas.stock import StockRealtime
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class RealtimeService(BaseService):
    """实时数据服务"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.realtime_repo = StockRealtimeRepository(db)
        
        # 初始化数据源
        self.akshare_client = AKShareClient()
        self.tushare_client = TushareClient()
    
    async def get_realtime_price(self, stock_code: str) -> Optional[StockRealtime]:
        """
        获取单个股票实时价格
        
        Args:
            stock_code: 股票代码
            
        Returns:
            实时价格数据
        """
        cache_key = self.generate_cache_key("realtime:price", stock_code)
        
        async def fetch_data():
            realtime_data = await self.realtime_repo.get_by_code(stock_code)
            
            if realtime_data:
                return StockRealtime.model_validate(realtime_data)
            
            return None
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_realtime_data_ttl,
        )
    
    async def get_batch_realtime_prices(self, stock_codes: List[str]) -> List[StockRealtime]:
        """
        批量获取股票实时价格
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            实时价格数据列表
        """
        cache_key = self.generate_cache_key("realtime:batch", ":".join(sorted(stock_codes)))
        
        async def fetch_data():
            realtime_data_list = await self.realtime_repo.get_by_codes(stock_codes)
            return [StockRealtime.model_validate(data) for data in realtime_data_list]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_realtime_data_ttl,
        )
    
    async def get_hot_stocks(self, limit: int = 20) -> List[StockRealtime]:
        """
        获取热门股票
        
        Args:
            limit: 限制数量
            
        Returns:
            热门股票列表
        """
        cache_key = self.generate_cache_key("realtime:hot", limit=limit)
        
        async def fetch_data():
            # 获取所有实时数据，然后按成交量排序
            hot_stocks = await self.realtime_repo.get_multi(skip=0, limit=limit * 2)
            
            # 按成交量排序
            sorted_stocks = sorted(
                hot_stocks,
                key=lambda x: x.amount or 0,
                reverse=True
            )[:limit]
            
            return [StockRealtime.model_validate(stock) for stock in sorted_stocks]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_realtime_data_ttl,
        )
    
    async def get_top_gainers(self, limit: int = 20) -> List[StockRealtime]:
        """
        获取涨幅榜
        
        Args:
            limit: 限制数量
            
        Returns:
            涨幅榜列表
        """
        cache_key = self.generate_cache_key("realtime:gainers", limit=limit)
        
        async def fetch_data():
            # 获取所有实时数据，然后按涨跌幅排序
            gainers = await self.realtime_repo.get_multi(skip=0, limit=limit * 2)
            
            # 按涨跌幅排序(降序)
            sorted_gainers = sorted(
                gainers,
                key=lambda x: x.pct_change or 0,
                reverse=True
            )[:limit]
            
            return [StockRealtime.model_validate(stock) for stock in sorted_gainers]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_realtime_data_ttl,
        )
    
    async def get_top_losers(self, limit: int = 20) -> List[StockRealtime]:
        """
        获取跌幅榜
        
        Args:
            limit: 限制数量
            
        Returns:
            跌幅榜列表
        """
        cache_key = self.generate_cache_key("realtime:losers", limit=limit)
        
        async def fetch_data():
            # 获取所有实时数据，然后按涨跌幅排序
            losers = await self.realtime_repo.get_multi(skip=0, limit=limit * 2)
            
            # 按涨跌幅排序(升序)
            sorted_losers = sorted(
                losers,
                key=lambda x: x.pct_change or 0
            )[:limit]
            
            return [StockRealtime.model_validate(stock) for stock in sorted_losers]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_realtime_data_ttl,
        )
    
    async def update_realtime_data(self, stock_codes: List[str]) -> int:
        """
        更新实时数据
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            更新成功的数量
        """
        success_count = 0
        
        try:
            # 优先使用AKShare获取实时数据
            data_source = self.akshare_client if self.akshare_client.enabled else self.tushare_client
            
            if not data_source.enabled:
                logger.warning("没有可用的数据源")
                return 0
            
            # 批量处理股票代码
            async def process_batch(batch_codes: List[str]) -> int:
                batch_success = 0
                for stock_code in batch_codes:
                    try:
                        realtime_data = await data_source.get_realtime_price(stock_code)
                        if realtime_data:
                            await self.realtime_repo.upsert_realtime_data(realtime_data)
                            
                            # 清除缓存
                            await self.invalidate_cache(f"realtime:price:{stock_code}")
                            
                            batch_success += 1
                    except Exception as e:
                        logger.error(f"更新实时数据失败({stock_code}): {e}")
                
                return batch_success
            
            # 分批处理
            success_count = await self.batch_process(
                stock_codes,
                process_batch,
                batch_size=50,
            )
            
            # 清除批量缓存
            await self.invalidate_cache("realtime:hot:*")
            await self.invalidate_cache("realtime:gainers:*")
            await self.invalidate_cache("realtime:losers:*")
            
            logger.info(f"实时数据更新完成，成功更新{success_count}/{len(stock_codes)}只股票")
            
        except Exception as e:
            logger.error(f"批量更新实时数据失败: {e}")
        
        return success_count
    
    async def get_market_summary(self) -> dict:
        """
        获取市场摘要
        
        Returns:
            市场摘要数据
        """
        cache_key = "realtime:market_summary"
        
        async def fetch_data():
            # 获取所有实时数据进行统计
            all_realtime = await self.realtime_repo.get_multi(skip=0, limit=5000)
            
            if not all_realtime:
                return {
                    "total_stocks": 0,
                    "up_count": 0,
                    "down_count": 0,
                    "flat_count": 0,
                    "avg_change": 0.0,
                }
            
            up_count = sum(1 for stock in all_realtime if (stock.pct_change or 0) > 0)
            down_count = sum(1 for stock in all_realtime if (stock.pct_change or 0) < 0)
            flat_count = len(all_realtime) - up_count - down_count
            
            total_change = sum(stock.pct_change or 0 for stock in all_realtime)
            avg_change = total_change / len(all_realtime) if all_realtime else 0
            
            return {
                "total_stocks": len(all_realtime),
                "up_count": up_count,
                "down_count": down_count,
                "flat_count": flat_count,
                "avg_change": round(avg_change, 2),
                "up_ratio": round(up_count / len(all_realtime) * 100, 2) if all_realtime else 0,
            }
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_realtime_data_ttl,
        )
