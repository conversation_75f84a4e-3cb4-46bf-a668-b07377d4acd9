"""
市场数据仓储
"""

from datetime import date
from typing import Optional, Sequence
from sqlalchemy import select, desc, and_
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.market import MarketIndex, Industry, IndustryDaily, TradingCalendar
from app.repositories.base import BaseRepository


class MarketIndexRepository(BaseRepository[MarketIndex]):
    """市场指数仓储"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(MarketIndex, db)
    
    async def get_latest_indices(self, codes: list[str]) -> Sequence[MarketIndex]:
        """获取最新指数数据"""
        # 获取最新交易日期
        latest_date_result = await self.db.execute(
            select(MarketIndex.trade_date)
            .where(MarketIndex.code.in_(codes))
            .order_by(desc(MarketIndex.trade_date))
            .limit(1)
        )
        latest_date = latest_date_result.scalar()
        
        if not latest_date:
            return []
        
        # 获取最新交易日的指数数据
        result = await self.db.execute(
            select(MarketIndex)
            .where(
                and_(
                    MarketIndex.code.in_(codes),
                    MarketIndex.trade_date == latest_date
                )
            )
        )
        return result.scalars().all()
    
    async def get_index_history(
        self,
        code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100,
    ) -> Sequence[MarketIndex]:
        """获取指数历史数据"""
        query = select(MarketIndex).where(MarketIndex.code == code)
        
        if start_date:
            query = query.where(MarketIndex.trade_date >= start_date)
        
        if end_date:
            query = query.where(MarketIndex.trade_date <= end_date)
        
        query = query.order_by(desc(MarketIndex.trade_date)).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()


class IndustryRepository(BaseRepository[Industry]):
    """行业仓储"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Industry, db)
    
    async def get_by_level(self, level: int) -> Sequence[Industry]:
        """根据级别获取行业"""
        result = await self.db.execute(
            select(Industry)
            .where(Industry.level == level)
            .order_by(Industry.code)
        )
        return result.scalars().all()
    
    async def get_children(self, parent_code: str) -> Sequence[Industry]:
        """获取子行业"""
        result = await self.db.execute(
            select(Industry)
            .where(Industry.parent_code == parent_code)
            .order_by(Industry.code)
        )
        return result.scalars().all()


class IndustryDailyRepository(BaseRepository[IndustryDaily]):
    """行业日线数据仓储"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(IndustryDaily, db)
    
    async def get_latest_industry_data(self, limit: int = 20) -> Sequence[IndustryDaily]:
        """获取最新行业数据"""
        # 获取最新交易日期
        latest_date_result = await self.db.execute(
            select(IndustryDaily.trade_date)
            .order_by(desc(IndustryDaily.trade_date))
            .limit(1)
        )
        latest_date = latest_date_result.scalar()
        
        if not latest_date:
            return []
        
        # 获取最新交易日的行业数据，按平均涨跌幅排序
        result = await self.db.execute(
            select(IndustryDaily)
            .where(IndustryDaily.trade_date == latest_date)
            .order_by(desc(IndustryDaily.avg_pct_change))
            .limit(limit)
        )
        return result.scalars().all()
    
    async def get_industry_history(
        self,
        industry_code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100,
    ) -> Sequence[IndustryDaily]:
        """获取行业历史数据"""
        query = select(IndustryDaily).where(IndustryDaily.industry_code == industry_code)
        
        if start_date:
            query = query.where(IndustryDaily.trade_date >= start_date)
        
        if end_date:
            query = query.where(IndustryDaily.trade_date <= end_date)
        
        query = query.order_by(desc(IndustryDaily.trade_date)).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()


class TradingCalendarRepository(BaseRepository[TradingCalendar]):
    """交易日历仓储"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(TradingCalendar, db)
    
    async def is_trading_day(self, cal_date: date) -> bool:
        """检查是否为交易日"""
        result = await self.db.execute(
            select(TradingCalendar.is_open)
            .where(TradingCalendar.cal_date == cal_date)
        )
        is_open = result.scalar()
        return is_open is True
    
    async def get_latest_trading_day(self) -> Optional[date]:
        """获取最新交易日"""
        result = await self.db.execute(
            select(TradingCalendar.cal_date)
            .where(TradingCalendar.is_open == True)
            .order_by(desc(TradingCalendar.cal_date))
            .limit(1)
        )
        return result.scalar()
    
    async def get_previous_trading_day(self, cal_date: date) -> Optional[date]:
        """获取前一个交易日"""
        result = await self.db.execute(
            select(TradingCalendar.cal_date)
            .where(
                and_(
                    TradingCalendar.is_open == True,
                    TradingCalendar.cal_date < cal_date
                )
            )
            .order_by(desc(TradingCalendar.cal_date))
            .limit(1)
        )
        return result.scalar()
    
    async def get_trading_days(
        self,
        start_date: date,
        end_date: date,
    ) -> Sequence[TradingCalendar]:
        """获取指定期间的交易日"""
        result = await self.db.execute(
            select(TradingCalendar)
            .where(
                and_(
                    TradingCalendar.cal_date >= start_date,
                    TradingCalendar.cal_date <= end_date,
                    TradingCalendar.is_open == True
                )
            )
            .order_by(TradingCalendar.cal_date)
        )
        return result.scalars().all()
