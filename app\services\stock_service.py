"""
股票业务逻辑服务
"""

from typing import List, Optional, Tuple
from datetime import date, datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.base import BaseService
from app.repositories.stock_repository import (
    StockRepository, StockPriceRepository, StockRealtimeRepository
)
from app.external.akshare_client import AKShareClient
from app.external.tushare_client import TushareClient
from app.schemas.stock import StockInfo, StockPrice, StockRealtime, StockDetail
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class StockService(BaseService):
    """股票服务"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.stock_repo = StockRepository(db)
        self.price_repo = StockPriceRepository(db)
        self.realtime_repo = StockRealtimeRepository(db)
        
        # 初始化数据源
        self.akshare_client = AKShareClient()
        self.tushare_client = TushareClient()
    
    async def search_stocks(
        self,
        keyword: Optional[str] = None,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        is_active: bool = True,
        page: int = 1,
        size: int = 20,
    ) -> Tuple[List[StockInfo], int]:
        """
        搜索股票
        
        Args:
            keyword: 关键词
            market: 市场
            industry: 行业
            is_active: 是否活跃
            page: 页码
            size: 每页大小
            
        Returns:
            股票列表和总数
        """
        cache_key = self.generate_cache_key(
            "stocks:search",
            keyword=keyword,
            market=market,
            industry=industry,
            is_active=is_active,
            page=page,
            size=size,
        )
        
        async def fetch_data():
            skip = (page - 1) * size
            stocks, total = await self.stock_repo.search_stocks(
                keyword=keyword,
                market=market,
                industry=industry,
                is_active=is_active,
                skip=skip,
                limit=size,
            )
            
            stock_infos = [StockInfo.model_validate(stock) for stock in stocks]
            return stock_infos, total
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_stock_info_ttl,
        )
    
    async def get_stock_detail(self, stock_code: str) -> Optional[StockDetail]:
        """
        获取股票详细信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票详细信息
        """
        cache_key = self.generate_cache_key("stock:detail", stock_code)
        
        async def fetch_data():
            # 获取股票基本信息
            stock = await self.stock_repo.get_by_code(stock_code)
            if not stock:
                return None
            
            # 获取实时价格
            realtime_data = await self.realtime_repo.get_by_code(stock_code)
            
            # 获取近期价格
            recent_prices = await self.price_repo.get_price_history(
                stock_code=stock_code,
                limit=30
            )
            
            # 构建详细信息
            stock_detail = StockDetail(
                **stock.to_dict(),
                latest_price=StockRealtime.model_validate(realtime_data) if realtime_data else None,
                recent_prices=[StockPrice.model_validate(price) for price in recent_prices]
            )
            
            return stock_detail
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_stock_info_ttl,
        )
    
    async def get_stock_prices(
        self,
        stock_code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100,
    ) -> List[StockPrice]:
        """
        获取股票价格历史
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            limit: 限制数量
            
        Returns:
            价格列表
        """
        cache_key = self.generate_cache_key(
            "stock:prices",
            stock_code,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
        )
        
        async def fetch_data():
            prices = await self.price_repo.get_price_history(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date,
                limit=limit,
            )
            
            return [StockPrice.model_validate(price) for price in prices]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_market_data_ttl,
        )
    
    async def get_realtime_price(self, stock_code: str) -> Optional[StockRealtime]:
        """
        获取实时价格
        
        Args:
            stock_code: 股票代码
            
        Returns:
            实时价格
        """
        cache_key = self.generate_cache_key("stock:realtime", stock_code)
        
        async def fetch_data():
            realtime_data = await self.realtime_repo.get_by_code(stock_code)
            
            if realtime_data:
                return StockRealtime.model_validate(realtime_data)
            
            return None
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_realtime_data_ttl,
        )
    
    async def get_batch_stocks(self, stock_codes: List[str]) -> List[StockInfo]:
        """
        批量获取股票信息
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            股票信息列表
        """
        cache_key = self.generate_cache_key("stocks:batch", ":".join(sorted(stock_codes)))
        
        async def fetch_data():
            stocks = await self.stock_repo.get_by_codes(stock_codes)
            return [StockInfo.model_validate(stock) for stock in stocks]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_stock_info_ttl,
        )
    
    async def get_batch_realtime_prices(self, stock_codes: List[str]) -> List[StockRealtime]:
        """
        批量获取实时价格
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            实时价格列表
        """
        cache_key = self.generate_cache_key("realtime:batch", ":".join(sorted(stock_codes)))
        
        async def fetch_data():
            realtime_data_list = await self.realtime_repo.get_by_codes(stock_codes)
            return [StockRealtime.model_validate(data) for data in realtime_data_list]
        
        return await self.get_cached_data(
            cache_key,
            fetch_data,
            ttl=settings.cache_realtime_data_ttl,
        )
    
    async def update_stock_data(self, stock_code: str) -> bool:
        """
        更新股票数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否更新成功
        """
        try:
            # 优先使用Tushare，备用AKShare
            data_source = self.tushare_client if self.tushare_client.enabled else self.akshare_client
            
            if not data_source.enabled:
                logger.warning("没有可用的数据源")
                return False
            
            # 更新股票基本信息
            stock_info = await data_source.get_stock_info(stock_code)
            if stock_info:
                existing_stock = await self.stock_repo.get_by_code(stock_code)
                if existing_stock:
                    await self.stock_repo.update(existing_stock.id, stock_info)
                else:
                    await self.stock_repo.create(stock_info)
            
            # 更新实时价格
            realtime_data = await data_source.get_realtime_price(stock_code)
            if realtime_data:
                await self.realtime_repo.upsert_realtime_data(realtime_data)
            
            # 清除相关缓存
            await self.invalidate_cache(f"stock:detail:{stock_code}")
            await self.invalidate_cache(f"stock:realtime:{stock_code}")
            
            logger.info(f"股票数据更新成功: {stock_code}")
            return True
            
        except Exception as e:
            logger.error(f"更新股票数据失败({stock_code}): {e}")
            return False
