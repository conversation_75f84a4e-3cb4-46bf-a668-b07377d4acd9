"""
用户相关模型
"""

from typing import Optional
from sqlalchemy import String, Bo<PERSON>an, Text
from sqlalchemy.orm import Mapped, mapped_column

from app.models.base import Base, TimestampMixin


class User(Base, TimestampMixin):
    """用户表"""
    
    __tablename__ = "users"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="主键ID")
    
    # 用户信息
    username: Mapped[str] = mapped_column(
        String(50), unique=True, index=True, comment="用户名"
    )
    email: Mapped[str] = mapped_column(
        String(100), unique=True, index=True, comment="邮箱"
    )
    hashed_password: Mapped[str] = mapped_column(String(255), comment="密码哈希")
    
    # 用户状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否激活")
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否超级用户")
    
    # 个人信息
    full_name: Mapped[Optional[str]] = mapped_column(String(100), comment="全名")
    phone: Mapped[Optional[str]] = mapped_column(String(20), comment="电话")
    
    # 偏好设置
    preferences: Mapped[Optional[str]] = mapped_column(Text, comment="用户偏好设置(JSON)")


class UserWatchlist(Base, TimestampMixin):
    """用户自选股表"""
    
    __tablename__ = "user_watchlists"
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="主键ID")
    
    # 用户ID
    user_id: Mapped[int] = mapped_column(comment="用户ID")
    
    # 股票代码
    stock_code: Mapped[str] = mapped_column(String(10), comment="股票代码")
    
    # 备注
    note: Mapped[Optional[str]] = mapped_column(String(200), comment="备注")
    
    # 排序
    sort_order: Mapped[int] = mapped_column(default=0, comment="排序")
