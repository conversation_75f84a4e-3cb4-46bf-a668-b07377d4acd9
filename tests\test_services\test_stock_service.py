"""
股票服务测试
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.stock_service import StockService
from app.models.stock import Stock, StockPrice, StockRealtime


class TestStockService:
    """股票服务测试类"""
    
    @pytest.mark.asyncio
    async def test_search_stocks_empty(self, db_session: AsyncSession):
        """测试搜索空股票列表"""
        service = StockService(db_session)
        
        stocks, total = await service.search_stocks()
        
        assert total == 0
        assert len(stocks) == 0
    
    @pytest.mark.asyncio
    async def test_search_stocks_with_data(
        self, 
        db_session: AsyncSession,
        sample_stock_data: dict
    ):
        """测试搜索股票列表"""
        # 创建测试数据
        stock = Stock(**sample_stock_data)
        db_session.add(stock)
        await db_session.commit()
        
        service = StockService(db_session)
        stocks, total = await service.search_stocks()
        
        assert total == 1
        assert len(stocks) == 1
        assert stocks[0].code == "000001"
        assert stocks[0].name == "平安银行"
    
    @pytest.mark.asyncio
    async def test_search_stocks_by_keyword(
        self, 
        db_session: AsyncSession,
        sample_stock_data: dict
    ):
        """测试按关键词搜索股票"""
        # 创建测试数据
        stock = Stock(**sample_stock_data)
        db_session.add(stock)
        await db_session.commit()
        
        service = StockService(db_session)
        
        # 按代码搜索
        stocks, total = await service.search_stocks(keyword="000001")
        assert total == 1
        assert stocks[0].code == "000001"
        
        # 按名称搜索
        stocks, total = await service.search_stocks(keyword="平安")
        assert total == 1
        assert stocks[0].name == "平安银行"
        
        # 搜索不存在的关键词
        stocks, total = await service.search_stocks(keyword="不存在")
        assert total == 0
    
    @pytest.mark.asyncio
    async def test_get_stock_detail_not_found(self, db_session: AsyncSession):
        """测试获取不存在的股票详情"""
        service = StockService(db_session)
        
        detail = await service.get_stock_detail("999999")
        
        assert detail is None
    
    @pytest.mark.asyncio
    async def test_get_stock_detail_success(
        self, 
        db_session: AsyncSession,
        sample_stock_data: dict,
        sample_realtime_data: dict,
        sample_price_data: dict
    ):
        """测试获取股票详情成功"""
        # 创建测试数据
        stock = Stock(**sample_stock_data)
        db_session.add(stock)
        
        realtime = StockRealtime(**sample_realtime_data)
        db_session.add(realtime)
        
        price = StockPrice(**sample_price_data)
        db_session.add(price)
        
        await db_session.commit()
        
        service = StockService(db_session)
        detail = await service.get_stock_detail("000001")
        
        assert detail is not None
        assert detail.code == "000001"
        assert detail.name == "平安银行"
        assert detail.latest_price is not None
        assert detail.latest_price.current_price == 10.2
        assert len(detail.recent_prices) == 1
    
    @pytest.mark.asyncio
    async def test_get_stock_prices(
        self, 
        db_session: AsyncSession,
        sample_price_data: dict
    ):
        """测试获取股票价格历史"""
        # 创建测试数据
        price = StockPrice(**sample_price_data)
        db_session.add(price)
        await db_session.commit()
        
        service = StockService(db_session)
        prices = await service.get_stock_prices("000001")
        
        assert len(prices) == 1
        assert prices[0].stock_code == "000001"
        assert prices[0].close_price == 10.2
    
    @pytest.mark.asyncio
    async def test_get_realtime_price(
        self, 
        db_session: AsyncSession,
        sample_realtime_data: dict
    ):
        """测试获取实时价格"""
        # 创建测试数据
        realtime = StockRealtime(**sample_realtime_data)
        db_session.add(realtime)
        await db_session.commit()
        
        service = StockService(db_session)
        realtime_price = await service.get_realtime_price("000001")
        
        assert realtime_price is not None
        assert realtime_price.stock_code == "000001"
        assert realtime_price.current_price == 10.2
    
    @pytest.mark.asyncio
    async def test_get_batch_stocks(
        self, 
        db_session: AsyncSession,
        sample_stock_data: dict
    ):
        """测试批量获取股票信息"""
        # 创建测试数据
        stock1 = Stock(**sample_stock_data)
        stock2_data = sample_stock_data.copy()
        stock2_data.update({"code": "000002", "name": "万科A"})
        stock2 = Stock(**stock2_data)
        
        db_session.add_all([stock1, stock2])
        await db_session.commit()
        
        service = StockService(db_session)
        stocks = await service.get_batch_stocks(["000001", "000002", "000003"])
        
        assert len(stocks) == 2  # 只有000001和000002存在
        codes = [stock.code for stock in stocks]
        assert "000001" in codes
        assert "000002" in codes
    
    @pytest.mark.asyncio
    async def test_get_batch_realtime_prices(
        self, 
        db_session: AsyncSession,
        sample_realtime_data: dict
    ):
        """测试批量获取实时价格"""
        # 创建测试数据
        realtime1 = StockRealtime(**sample_realtime_data)
        realtime2_data = sample_realtime_data.copy()
        realtime2_data.update({"stock_code": "000002", "current_price": 20.5})
        realtime2 = StockRealtime(**realtime2_data)
        
        db_session.add_all([realtime1, realtime2])
        await db_session.commit()
        
        service = StockService(db_session)
        realtime_prices = await service.get_batch_realtime_prices(["000001", "000002"])
        
        assert len(realtime_prices) == 2
        codes = [price.stock_code for price in realtime_prices]
        assert "000001" in codes
        assert "000002" in codes
    
    @pytest.mark.asyncio
    async def test_pagination(self, db_session: AsyncSession):
        """测试分页功能"""
        # 创建多个测试股票
        stocks = []
        for i in range(25):
            stock_data = {
                "code": f"{i:06d}",
                "name": f"测试股票{i}",
                "market": "SZ",
                "is_active": True,
                "is_st": False,
            }
            stocks.append(Stock(**stock_data))
        
        db_session.add_all(stocks)
        await db_session.commit()
        
        service = StockService(db_session)
        
        # 测试第一页
        stocks_page1, total = await service.search_stocks(page=1, size=10)
        assert total == 25
        assert len(stocks_page1) == 10
        
        # 测试第二页
        stocks_page2, total = await service.search_stocks(page=2, size=10)
        assert total == 25
        assert len(stocks_page2) == 10
        
        # 测试第三页
        stocks_page3, total = await service.search_stocks(page=3, size=10)
        assert total == 25
        assert len(stocks_page3) == 5  # 最后一页只有5个
