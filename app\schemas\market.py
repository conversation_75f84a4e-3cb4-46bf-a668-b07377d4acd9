"""
市场数据相关模式
"""

from datetime import date
from decimal import Decimal
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class MarketIndex(BaseModel):
    """市场指数模式"""
    
    code: str = Field(description="指数代码")
    name: str = Field(description="指数名称")
    trade_date: date = Field(description="交易日期")
    open_price: Optional[Decimal] = Field(default=None, description="开盘点数")
    high_price: Optional[Decimal] = Field(default=None, description="最高点数")
    low_price: Optional[Decimal] = Field(default=None, description="最低点数")
    close_price: Optional[Decimal] = Field(default=None, description="收盘点数")
    pre_close: Optional[Decimal] = Field(default=None, description="前收盘点数")
    change: Optional[Decimal] = Field(default=None, description="涨跌点数")
    pct_change: Optional[Decimal] = Field(default=None, description="涨跌幅(%)")
    volume: Optional[Decimal] = Field(default=None, description="成交量(万手)")
    amount: Optional[Decimal] = Field(default=None, description="成交额(亿元)")
    
    model_config = ConfigDict(from_attributes=True)


class Industry(BaseModel):
    """行业模式"""
    
    code: str = Field(description="行业代码")
    name: str = Field(description="行业名称")
    level: int = Field(description="行业级别")
    parent_code: Optional[str] = Field(default=None, description="父级行业代码")
    stock_count: int = Field(description="股票数量")
    
    model_config = ConfigDict(from_attributes=True)


class IndustryDaily(BaseModel):
    """行业日线数据模式"""
    
    industry_code: str = Field(description="行业代码")
    trade_date: date = Field(description="交易日期")
    up_count: int = Field(description="上涨股票数")
    down_count: int = Field(description="下跌股票数")
    flat_count: int = Field(description="平盘股票数")
    avg_pct_change: Optional[Decimal] = Field(default=None, description="平均涨跌幅(%)")
    total_volume: Optional[Decimal] = Field(default=None, description="总成交量(万手)")
    total_amount: Optional[Decimal] = Field(default=None, description="总成交额(亿元)")
    
    model_config = ConfigDict(from_attributes=True)


class TradingCalendar(BaseModel):
    """交易日历模式"""
    
    cal_date: date = Field(description="日期")
    is_open: bool = Field(description="是否交易日")
    pretrade_date: Optional[date] = Field(default=None, description="前一个交易日")
    
    model_config = ConfigDict(from_attributes=True)


class MarketOverview(BaseModel):
    """市场概览模式"""
    
    indices: list[MarketIndex] = Field(description="主要指数")
    hot_industries: list[IndustryDaily] = Field(description="热门行业")
    market_stats: dict = Field(description="市场统计")
    
    model_config = ConfigDict(from_attributes=True)
