"""
Redis缓存配置和管理
"""

import json
import pickle
from typing import Any, Optional, Union
from redis.asyncio import Redis
from app.core.config import settings


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.redis: Optional[Redis] = None
    
    async def init_cache(self) -> None:
        """初始化缓存连接"""
        self.redis = Redis.from_url(
            settings.redis_url,
            encoding="utf-8",
            decode_responses=False,
        )
    
    async def close_cache(self) -> None:
        """关闭缓存连接"""
        if self.redis:
            await self.redis.close()
    
    async def get(self, key: str, use_json: bool = True) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            use_json: 是否使用JSON序列化
            
        Returns:
            缓存值或None
        """
        if not self.redis:
            return None
        
        try:
            value = await self.redis.get(key)
            if value is None:
                return None
            
            if use_json:
                return json.loads(value)
            else:
                return pickle.loads(value)
        except Exception:
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None, 
        use_json: bool = True
    ) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间(秒)
            use_json: 是否使用JSON序列化
            
        Returns:
            是否设置成功
        """
        if not self.redis:
            return False
        
        try:
            if use_json:
                serialized_value = json.dumps(value, ensure_ascii=False)
            else:
                serialized_value = pickle.dumps(value)
            
            if ttl:
                await self.redis.setex(key, ttl, serialized_value)
            else:
                await self.redis.set(key, serialized_value)
            
            return True
        except Exception:
            return False
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        if not self.redis:
            return False
        
        try:
            await self.redis.delete(key)
            return True
        except Exception:
            return False
    
    async def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        if not self.redis:
            return False
        
        try:
            return bool(await self.redis.exists(key))
        except Exception:
            return False
    
    async def set_hash(self, key: str, field: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置哈希缓存
        
        Args:
            key: 缓存键
            field: 字段名
            value: 字段值
            ttl: 过期时间(秒)
            
        Returns:
            是否设置成功
        """
        if not self.redis:
            return False
        
        try:
            serialized_value = json.dumps(value, ensure_ascii=False)
            await self.redis.hset(key, field, serialized_value)
            
            if ttl:
                await self.redis.expire(key, ttl)
            
            return True
        except Exception:
            return False
    
    async def get_hash(self, key: str, field: str) -> Optional[Any]:
        """
        获取哈希缓存值
        
        Args:
            key: 缓存键
            field: 字段名
            
        Returns:
            字段值或None
        """
        if not self.redis:
            return None
        
        try:
            value = await self.redis.hget(key, field)
            if value is None:
                return None
            
            return json.loads(value)
        except Exception:
            return None


# 全局缓存管理器实例
cache_manager = CacheManager()
