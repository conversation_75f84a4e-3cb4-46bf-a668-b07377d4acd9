"""
简化的启动脚本，用于测试基本功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'pydantic',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print("pip install " + " ".join(missing_packages))
        return False
    
    return True

def create_simple_app():
    """创建简化的FastAPI应用"""
    from fastapi import FastAPI
    
    app = FastAPI(
        title="量化交易系统",
        version="0.1.0",
        description="量化交易系统后端API服务"
    )
    
    @app.get("/")
    async def root():
        return {"message": "欢迎使用量化交易系统", "status": "running"}
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "service": "量化交易系统"}
    
    return app

def main():
    """主函数"""
    print("检查依赖包...")
    
    if not check_dependencies():
        sys.exit(1)
    
    print("依赖包检查通过!")
    
    try:
        # 加载环境变量
        from dotenv import load_dotenv
        load_dotenv()
        
        # 创建应用
        app = create_simple_app()
        
        # 启动服务器
        import uvicorn
        print("启动服务器...")
        print("访问地址: http://localhost:8000")
        print("API文档: http://localhost:8000/docs")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=True
        )
        
    except Exception as e:
        print(f"启动失败: {e}")
        print("\n请确保已安装所有依赖包:")
        print("pip install -r requirements.txt")
        sys.exit(1)

if __name__ == "__main__":
    main()
