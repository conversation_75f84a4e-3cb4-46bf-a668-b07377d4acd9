"""
API v1路由汇总
"""

from fastapi import APIRouter

from app.api.v1.endpoints import stocks, realtime, market

api_router = APIRouter()

# 包含各个端点路由
api_router.include_router(
    stocks.router, 
    prefix="/stocks", 
    tags=["stocks"],
    responses={404: {"description": "Not found"}},
)

api_router.include_router(
    realtime.router, 
    prefix="/realtime", 
    tags=["realtime"],
    responses={404: {"description": "Not found"}},
)

api_router.include_router(
    market.router, 
    prefix="/market", 
    tags=["market"],
    responses={404: {"description": "Not found"}},
)
