"""
基础服务类
"""

from typing import Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.cache import cache_manager
from app.core.logging import get_logger

logger = get_logger(__name__)


class BaseService:
    """基础服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.cache = cache_manager
    
    async def get_cached_data(
        self,
        cache_key: str,
        fetch_func: callable,
        ttl: Optional[int] = None,
        use_json: bool = True,
        *args,
        **kwargs
    ) -> Any:
        """
        获取缓存数据，如果缓存不存在则调用函数获取并缓存
        
        Args:
            cache_key: 缓存键
            fetch_func: 获取数据的函数
            ttl: 缓存过期时间(秒)
            use_json: 是否使用JSON序列化
            *args: 传递给fetch_func的位置参数
            **kwargs: 传递给fetch_func的关键字参数
            
        Returns:
            数据
        """
        try:
            # 尝试从缓存获取数据
            cached_data = await self.cache.get(cache_key, use_json=use_json)
            if cached_data is not None:
                logger.debug(f"从缓存获取数据: {cache_key}")
                return cached_data
            
            # 缓存不存在，调用函数获取数据
            logger.debug(f"缓存未命中，获取新数据: {cache_key}")
            data = await fetch_func(*args, **kwargs)
            
            # 将数据存入缓存
            if data is not None:
                await self.cache.set(cache_key, data, ttl=ttl, use_json=use_json)
                logger.debug(f"数据已缓存: {cache_key}")
            
            return data
            
        except Exception as e:
            logger.error(f"获取缓存数据失败: {cache_key}, 错误: {e}")
            # 缓存失败时直接调用函数获取数据
            return await fetch_func(*args, **kwargs)
    
    async def invalidate_cache(self, cache_key: str) -> bool:
        """
        使缓存失效
        
        Args:
            cache_key: 缓存键
            
        Returns:
            是否成功
        """
        try:
            await self.cache.delete(cache_key)
            logger.debug(f"缓存已失效: {cache_key}")
            return True
        except Exception as e:
            logger.error(f"使缓存失效失败: {cache_key}, 错误: {e}")
            return False
    
    def generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """
        生成缓存键
        
        Args:
            prefix: 前缀
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            缓存键
        """
        key_parts = [prefix]
        
        # 添加位置参数
        for arg in args:
            key_parts.append(str(arg))
        
        # 添加关键字参数
        for key, value in sorted(kwargs.items()):
            if value is not None:
                key_parts.append(f"{key}:{value}")
        
        return ":".join(key_parts)
    
    async def batch_process(
        self,
        items: list[Any],
        process_func: callable,
        batch_size: int = 100,
        *args,
        **kwargs
    ) -> list[Any]:
        """
        批量处理数据
        
        Args:
            items: 要处理的项目列表
            process_func: 处理函数
            batch_size: 批次大小
            *args: 传递给process_func的位置参数
            **kwargs: 传递给process_func的关键字参数
            
        Returns:
            处理结果列表
        """
        results = []
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            try:
                batch_results = await process_func(batch, *args, **kwargs)
                if isinstance(batch_results, list):
                    results.extend(batch_results)
                else:
                    results.append(batch_results)
            except Exception as e:
                logger.error(f"批量处理失败: 批次 {i//batch_size + 1}, 错误: {e}")
                # 可以选择继续处理下一批次或抛出异常
                continue
        
        return results
