"""
测试配置文件
"""

import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import get_db, Base
from app.core.config import settings

# 测试数据库URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# 创建测试数据库引擎
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

# 创建测试会话工厂
TestSessionLocal = async_sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with TestSessionLocal() as session:
        yield session
    
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest_asyncio.fixture
async def client(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """创建测试客户端"""
    
    async def override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def sample_stock_data():
    """示例股票数据"""
    return {
        "code": "000001",
        "name": "平安银行",
        "market": "SZ",
        "industry": "银行",
        "sector": "金融",
        "full_name": "平安银行股份有限公司",
        "is_active": True,
        "is_st": False,
    }


@pytest_asyncio.fixture
async def sample_price_data():
    """示例价格数据"""
    from datetime import date
    return {
        "stock_code": "000001",
        "trade_date": date.today(),
        "open_price": 10.0,
        "high_price": 10.5,
        "low_price": 9.8,
        "close_price": 10.2,
        "pre_close": 10.0,
        "volume": 1000000,
        "amount": 10200000,
        "change": 0.2,
        "pct_change": 2.0,
    }


@pytest_asyncio.fixture
async def sample_realtime_data():
    """示例实时数据"""
    from datetime import datetime
    return {
        "stock_code": "000001",
        "current_price": 10.2,
        "pre_close": 10.0,
        "change": 0.2,
        "pct_change": 2.0,
        "volume": 1000000,
        "amount": 10200000,
        "bid1": 10.1,
        "bid1_volume": 1000,
        "ask1": 10.3,
        "ask1_volume": 1000,
        "update_time": datetime.now(),
    }
