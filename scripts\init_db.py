"""
数据库初始化脚本
"""

import asyncio
from app.core.database import init_db
from app.core.logging import get_logger

logger = get_logger(__name__)


async def main():
    """初始化数据库"""
    try:
        logger.info("开始初始化数据库...")
        await init_db()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
