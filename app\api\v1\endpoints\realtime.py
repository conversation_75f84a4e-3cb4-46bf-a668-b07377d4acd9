"""
实时数据API端点
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query

from app.schemas.stock import StockRealtime, BatchStockQuery
from app.schemas.response import BaseResponse
from app.repositories.stock_repository import StockRealtimeRepository
from app.api.deps import get_stock_realtime_repository

router = APIRouter()


@router.get("/{stock_code}", response_model=BaseResponse[StockRealtime])
async def get_realtime_price(
    stock_code: str,
    realtime_repo: StockRealtimeRepository = Depends(get_stock_realtime_repository),
):
    """
    获取单个股票实时价格
    
    - **stock_code**: 股票代码
    """
    try:
        realtime_data = await realtime_repo.get_by_code(stock_code)
        
        if not realtime_data:
            raise HTTPException(status_code=404, detail="未找到实时数据")
        
        realtime_info = StockRealtime.model_validate(realtime_data)
        
        return BaseResponse.success(realtime_info)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实时价格失败: {str(e)}")


@router.post("/batch", response_model=BaseResponse[List[StockRealtime]])
async def get_realtime_prices_batch(
    query: BatchStockQuery,
    realtime_repo: StockRealtimeRepository = Depends(get_stock_realtime_repository),
):
    """
    批量获取股票实时价格
    
    - **stock_codes**: 股票代码列表(最多100个)
    """
    try:
        if len(query.stock_codes) > 100:
            raise HTTPException(status_code=400, detail="股票代码数量不能超过100个")
        
        realtime_data_list = await realtime_repo.get_by_codes(query.stock_codes)
        
        realtime_infos = [
            StockRealtime.model_validate(data) for data in realtime_data_list
        ]
        
        return BaseResponse.success(realtime_infos)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量获取实时价格失败: {str(e)}")


@router.get("/prices/hot")
async def get_hot_stocks(
    limit: int = Query(20, description="数量限制", ge=1, le=50),
    realtime_repo: StockRealtimeRepository = Depends(get_stock_realtime_repository),
):
    """
    获取热门股票实时价格
    
    - **limit**: 数量限制
    """
    try:
        # 这里可以根据成交量、涨跌幅等指标筛选热门股票
        # 暂时返回前N个有实时数据的股票
        hot_stocks = await realtime_repo.get_multi(skip=0, limit=limit)
        
        hot_stock_infos = [
            StockRealtime.model_validate(stock) for stock in hot_stocks
        ]
        
        return BaseResponse.success(hot_stock_infos)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门股票失败: {str(e)}")


@router.get("/prices/gainers")
async def get_top_gainers(
    limit: int = Query(20, description="数量限制", ge=1, le=50),
    realtime_repo: StockRealtimeRepository = Depends(get_stock_realtime_repository),
):
    """
    获取涨幅榜
    
    - **limit**: 数量限制
    """
    try:
        # 这里需要根据涨跌幅排序
        # 由于BaseRepository没有复杂排序功能，这里简化处理
        gainers = await realtime_repo.get_multi(skip=0, limit=limit)
        
        # 按涨跌幅排序
        sorted_gainers = sorted(
            gainers, 
            key=lambda x: x.pct_change or 0, 
            reverse=True
        )
        
        gainer_infos = [
            StockRealtime.model_validate(stock) for stock in sorted_gainers
        ]
        
        return BaseResponse.success(gainer_infos)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取涨幅榜失败: {str(e)}")


@router.get("/prices/losers")
async def get_top_losers(
    limit: int = Query(20, description="数量限制", ge=1, le=50),
    realtime_repo: StockRealtimeRepository = Depends(get_stock_realtime_repository),
):
    """
    获取跌幅榜
    
    - **limit**: 数量限制
    """
    try:
        # 这里需要根据涨跌幅排序
        losers = await realtime_repo.get_multi(skip=0, limit=limit)
        
        # 按涨跌幅排序(升序)
        sorted_losers = sorted(
            losers, 
            key=lambda x: x.pct_change or 0
        )
        
        loser_infos = [
            StockRealtime.model_validate(stock) for stock in sorted_losers
        ]
        
        return BaseResponse.success(loser_infos)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取跌幅榜失败: {str(e)}")
