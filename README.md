# 量化交易系统后端

一个基于FastAPI的专业量化交易系统后端服务，提供股票信息查询、实时价格监控、市场数据分析等功能。

## 功能特性

- 🚀 **高性能**: 基于FastAPI和异步编程，支持高并发请求
- 📊 **数据丰富**: 集成Tushare、AKShare等多个数据源
- 🔄 **实时更新**: 支持实时股票价格和市场数据更新
- 💾 **智能缓存**: Redis缓存机制，提升数据访问速度
- 🏗️ **架构清晰**: 分层架构设计，代码可维护性强
- 📝 **文档完善**: 自动生成API文档，支持在线调试
- 🧪 **测试覆盖**: 完整的单元测试和集成测试
- 🔒 **安全可靠**: JWT认证，数据加密存储

## 技术栈

- **Web框架**: FastAPI 0.104+
- **数据库**: PostgreSQL + SQLAlchemy 2.0
- **缓存**: Redis
- **任务队列**: Celery
- **数据源**: Tushare, AKShare
- **日志**: Structlog
- **测试**: Pytest
- **代码质量**: Black, isort, flake8, mypy

## 项目结构

```
backend/
├── app/                          # 应用主目录
│   ├── core/                     # 核心配置和工具
│   ├── api/                      # API路由层
│   ├── models/                   # 数据模型
│   ├── schemas/                  # Pydantic模式
│   ├── services/                 # 业务逻辑层
│   ├── repositories/             # 数据访问层
│   ├── external/                 # 外部数据源
│   ├── utils/                    # 工具函数
│   └── tasks/                    # 异步任务
├── tests/                        # 测试目录
├── migrations/                   # 数据库迁移
├── scripts/                      # 脚本目录
└── requirements/                 # 依赖文件
```

## 快速开始

### 环境要求

- Python 3.11+
- PostgreSQL 13+
- Redis 6+

### 安装依赖

```bash
# 使用Poetry安装依赖
poetry install

# 或使用pip安装
pip install -r requirements/dev.txt
```

### 环境配置

1. 复制环境变量文件：
```bash
cp .env.example .env
```

2. 编辑`.env`文件，配置数据库和Redis连接信息

### 数据库初始化

```bash
# 运行数据库迁移
alembic upgrade head

# 或使用初始化脚本
python scripts/init_db.py
```

### 启动服务

```bash
# 开发模式启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 或使用Python直接运行
python -m app.main
```

### 访问API文档

启动服务后，访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/api/v1/docs
- ReDoc: http://localhost:8000/api/v1/redoc

## API接口

### 股票信息查询

- `GET /api/v1/stocks/` - 获取股票列表
- `GET /api/v1/stocks/{stock_code}` - 获取单个股票详细信息
- `GET /api/v1/stocks/{stock_code}/realtime` - 获取实时价格信息

### 市场数据

- `GET /api/v1/market/indices` - 获取市场指数
- `GET /api/v1/market/sectors` - 获取行业板块数据
- `GET /api/v1/market/hotlist` - 获取热门股票

### 实时数据

- `GET /api/v1/realtime/prices` - 批量获取实时价格
- `WebSocket /api/v1/realtime/ws` - 实时数据推送

## 开发指南

### 代码规范

项目使用以下工具确保代码质量：

```bash
# 代码格式化
black app/ tests/
isort app/ tests/

# 代码检查
flake8 app/ tests/
mypy app/

# 运行所有检查
pre-commit run --all-files
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html

# 运行特定测试
pytest tests/test_api/test_stocks.py
```

### 数据库迁移

```bash
# 创建新的迁移文件
alembic revision --autogenerate -m "描述信息"

# 应用迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t quantitative-trading-backend .

# 运行容器
docker-compose up -d
```

### 生产环境配置

1. 设置环境变量 `DEBUG=False`
2. 配置生产数据库连接
3. 设置强密码和密钥
4. 配置反向代理（Nginx）
5. 设置监控和日志收集

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱: <EMAIL>
- 项目地址: https://github.com/your-username/quantitative-trading-backend
